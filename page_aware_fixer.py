#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能页面感知修复器
基于MCP分析当前页面状态，智能修复导航路径和选择器
"""

import json
import asyncio
from typing import Dict, List, Optional, Tuple
from playwright.async_api import async_playwright
from guide_detector import smart_skip_guides

class PageAwareFixer:
    """智能页面感知修复器"""
    
    def __init__(self):
        self.page = None
        self.browser = None
        self.context = None
        self.playwright = None
        self.current_page_state = {}
        self.navigation_history = []
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(headless=False)
        self.context = await self.browser.new_context()
        self.page = await self.context.new_page()
        self.page.set_default_timeout(30000)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        try:
            if self.page and not self.page.is_closed():
                await self.page.close()
        except:
            pass
        
        try:
            if self.context:
                await self.context.close()
        except:
            pass
        
        try:
            if self.browser:
                await self.browser.close()
        except:
            pass
        
        try:
            if self.playwright:
                await self.playwright.stop()
        except:
            pass
    
    async def fix_script_with_page_awareness(self, actions_file: str) -> str:
        """基于页面感知修复脚本"""
        print(f"🧠 开始智能页面感知修复: {actions_file}")
        
        # 加载原始脚本
        with open(actions_file, 'r', encoding='utf-8') as f:
            actions = json.load(f)
        
        # 初始化页面
        await self._initialize_page()
        
        # 智能修复每个操作
        fixed_actions = []
        fixed_count = 0
        
        for i, action in enumerate(actions, 1):
            print(f"\n🔍 分析步骤 {i}/{len(actions)}: {action.get('action')} - {action.get('narration', '')[:50]}...")
            
            # 分析当前页面状态
            await self._analyze_current_page()
            
            # 智能修复操作
            fixed_action = await self._fix_action_with_awareness(action, i)
            
            # 检查是否有修复
            if self._is_action_fixed(action, fixed_action):
                fixed_count += 1
                print(f"✅ 步骤 {i} 智能修复成功")
            
            fixed_actions.append(fixed_action)
            
            # 如果是导航操作，更新页面状态
            if fixed_action.get('action') in ['click', 'goto']:
                await self._update_navigation_state(fixed_action)
        
        # 保存修复后的脚本
        fixed_file = actions_file.replace('.json', '_page_aware_fixed.json')
        with open(fixed_file, 'w', encoding='utf-8') as f:
            json.dump(fixed_actions, f, ensure_ascii=False, indent=2)
        
        print(f"\n📊 页面感知修复完成: 修复了 {fixed_count}/{len(actions)} 个步骤")
        print(f"✅ 修复后文件保存到: {fixed_file}")
        return fixed_file
    
    async def _initialize_page(self):
        """初始化页面"""
        print("🌐 初始化页面...")
        
        # 访问网站
        await self.page.goto("http://xiaoyuanst.com", wait_until='networkidle')
        
        # 点击体验系统
        await self.page.click("text=体验系统")
        await self.page.wait_for_timeout(3000)
        
        # 智能跳过引导
        guide_result = await smart_skip_guides(self.page)
        print(f"✅ 引导跳过完成: {guide_result['final_state']}")
        
        # 等待页面稳定
        await self.page.wait_for_timeout(2000)
        
        # 分析初始页面状态
        await self._analyze_current_page()
    
    async def _analyze_current_page(self):
        """分析当前页面状态"""
        try:
            # 获取当前URL
            current_url = self.page.url
            
            # 获取页面标题
            title = await self.page.title()
            
            # 分析左侧导航菜单
            nav_items = await self._get_navigation_items()
            
            # 分析页面可点击元素
            clickable_elements = await self._get_clickable_elements()
            
            # 分析面包屑导航
            breadcrumbs = await self._get_breadcrumbs()
            
            self.current_page_state = {
                'url': current_url,
                'title': title,
                'navigation_items': nav_items,
                'clickable_elements': clickable_elements,
                'breadcrumbs': breadcrumbs,
                'timestamp': asyncio.get_event_loop().time()
            }
            
            print(f"📍 当前页面状态:")
            print(f"   URL: {current_url}")
            print(f"   标题: {title}")
            print(f"   导航项: {len(nav_items)} 个")
            print(f"   可点击元素: {len(clickable_elements)} 个")
            
        except Exception as e:
            print(f"⚠️ 页面状态分析失败: {e}")
    
    async def _get_navigation_items(self) -> List[Dict]:
        """获取左侧导航菜单项"""
        nav_items = []
        
        try:
            # 查找导航菜单的多种可能选择器
            nav_selectors = [
                '.sidebar .nav-link',
                '.sidebar a',
                '.menu .nav-link',
                '.navigation a',
                'nav a',
                '.nav-item a'
            ]
            
            for selector in nav_selectors:
                elements = await self.page.query_selector_all(selector)
                if elements:
                    for element in elements:
                        if await element.is_visible():
                            text = await element.inner_text()
                            href = await element.get_attribute('href')
                            if text and text.strip():
                                nav_items.append({
                                    'text': text.strip(),
                                    'href': href,
                                    'selector': selector
                                })
                    break  # 找到有效的导航项就停止
            
        except Exception as e:
            print(f"⚠️ 导航项分析失败: {e}")
        
        return nav_items
    
    async def _get_clickable_elements(self) -> List[Dict]:
        """获取页面可点击元素"""
        clickable_elements = []
        
        try:
            # 查找各种可点击元素
            selectors = [
                'button',
                'a',
                '[role="button"]',
                '.btn',
                'input[type="submit"]',
                '.clickable'
            ]
            
            for selector in selectors:
                elements = await self.page.query_selector_all(selector)
                for element in elements:
                    if await element.is_visible():
                        text = await element.inner_text()
                        if text and text.strip():
                            clickable_elements.append({
                                'text': text.strip(),
                                'selector': selector,
                                'tag': await element.evaluate('el => el.tagName.toLowerCase()')
                            })
        
        except Exception as e:
            print(f"⚠️ 可点击元素分析失败: {e}")
        
        return clickable_elements
    
    async def _get_breadcrumbs(self) -> List[str]:
        """获取面包屑导航"""
        breadcrumbs = []
        
        try:
            breadcrumb_selectors = [
                '.breadcrumb li',
                '.breadcrumb a',
                '.breadcrumb-item',
                '.page-breadcrumb li'
            ]
            
            for selector in breadcrumb_selectors:
                elements = await self.page.query_selector_all(selector)
                if elements:
                    for element in elements:
                        text = await element.inner_text()
                        if text and text.strip():
                            breadcrumbs.append(text.strip())
                    break
        
        except Exception as e:
            print(f"⚠️ 面包屑分析失败: {e}")
        
        return breadcrumbs
    
    async def _fix_action_with_awareness(self, action: Dict, step_num: int) -> Dict:
        """基于页面感知修复操作"""
        fixed_action = action.copy()
        
        action_type = action.get('action')
        
        if action_type == 'click':
            fixed_selector = await self._fix_click_with_awareness(action)
            if fixed_selector:
                fixed_action['selector'] = fixed_selector
                if fixed_selector != action.get('selector'):
                    fixed_action['original_selector'] = action.get('selector')
                    fixed_action['fix_method'] = 'page_aware'
        
        elif action_type == 'goto':
            # goto操作通常不需要修复
            pass
        
        elif action_type == 'wait':
            # wait操作通常不需要修复
            pass
        
        elif action_type == 'click_skip_guide':
            # 跳过引导操作已经在初始化时处理
            pass
        
        return fixed_action
    
    async def _fix_click_with_awareness(self, action: Dict) -> Optional[str]:
        """基于页面感知修复点击操作"""
        original_selector = action.get('selector', '')
        if not original_selector:
            return original_selector
        
        # 提取目标文本
        target_text = self._extract_text_from_selector(original_selector)
        if not target_text:
            return original_selector
        
        print(f"🎯 智能分析目标: {target_text}")
        
        # 1. 检查目标元素是否在当前页面存在
        if await self._element_exists_on_page(target_text):
            print(f"✅ 目标元素在当前页面存在: {target_text}")
            # 生成精确选择器
            precise_selector = await self._generate_precise_selector(target_text)
            return precise_selector or original_selector
        
        # 2. 目标元素不存在，需要导航
        print(f"🔍 目标元素不在当前页面，分析导航路径: {target_text}")
        navigation_path = await self._find_navigation_path(target_text)
        
        if navigation_path:
            print(f"🗺️ 找到导航路径: {' → '.join(navigation_path)}")
            # 返回第一步的导航选择器
            first_step = navigation_path[0]
            nav_selector = await self._generate_navigation_selector(first_step)
            return nav_selector or original_selector
        
        print(f"❌ 无法找到导航路径: {target_text}")
        return original_selector
    
    async def _element_exists_on_page(self, text: str) -> bool:
        """检查元素是否在当前页面存在"""
        # 检查导航项
        for nav_item in self.current_page_state.get('navigation_items', []):
            if text in nav_item['text'] or nav_item['text'] in text:
                return True
        
        # 检查可点击元素
        for element in self.current_page_state.get('clickable_elements', []):
            if text in element['text'] or element['text'] in text:
                return True
        
        # 直接在页面上搜索
        try:
            elements = await self.page.query_selector_all(f'text="{text}"')
            for element in elements:
                if await element.is_visible():
                    return True
        except:
            pass
        
        return False
    
    async def _generate_precise_selector(self, text: str) -> Optional[str]:
        """为存在的元素生成精确选择器"""
        # 检查导航项
        for nav_item in self.current_page_state.get('navigation_items', []):
            if text in nav_item['text'] or nav_item['text'] in text:
                return f"a:has-text('{nav_item['text']}')"
        
        # 检查可点击元素
        for element in self.current_page_state.get('clickable_elements', []):
            if text in element['text'] or element['text'] in text:
                if element['tag'] == 'button':
                    return f"button:has-text('{element['text']}')"
                elif element['tag'] == 'a':
                    return f"a:has-text('{element['text']}')"
                else:
                    return f"text='{element['text']}'"
        
        return None
    
    async def _find_navigation_path(self, target_text: str) -> Optional[List[str]]:
        """找到到达目标的导航路径"""
        # 供应商模块的导航路径映射
        navigation_paths = {
            '供应商分类': ['供应链', '供应商分类'],
            '供应商列表': ['供应链', '供应商列表'],
            '供应商': ['供应链', '供应商'],
            '产品管理': ['供应链', '产品管理'],
            '添加分类': ['供应链', '供应商分类', '添加分类'],
            '添加供应商': ['供应链', '供应商列表', '添加供应商'],
            '添加产品': ['供应链', '产品管理', '添加产品'],
        }
        
        # 查找匹配的路径
        for key, path in navigation_paths.items():
            if target_text in key or key in target_text:
                return path
        
        # 如果没有预定义路径，尝试智能推断
        if '供应商' in target_text or '分类' in target_text or '产品' in target_text:
            return ['供应链']
        
        return None
    
    async def _generate_navigation_selector(self, nav_text: str) -> Optional[str]:
        """为导航项生成选择器"""
        # 在当前页面的导航项中查找
        for nav_item in self.current_page_state.get('navigation_items', []):
            if nav_text in nav_item['text'] or nav_item['text'] in nav_text:
                return f"a:has-text('{nav_item['text']}')"
        
        # 生成通用导航选择器
        return f"text='{nav_text}'"
    
    def _extract_text_from_selector(self, selector: str) -> str:
        """从选择器中提取文本"""
        import re
        
        # text= 格式
        text_match = re.search(r'text=(.+)', selector)
        if text_match:
            return text_match.group(1).strip('"\'')
        
        # :has-text() 格式
        has_text_match = re.search(r':has-text\(["\']([^"\']+)["\']', selector)
        if has_text_match:
            return has_text_match.group(1)
        
        return ''
    
    def _is_action_fixed(self, original: Dict, fixed: Dict) -> bool:
        """检查操作是否被修复"""
        return (
            fixed.get('selector') != original.get('selector') or
            'fix_method' in fixed
        )
    
    async def _update_navigation_state(self, action: Dict):
        """更新导航状态"""
        if action.get('action') == 'click':
            selector = action.get('selector', '')
            text = self._extract_text_from_selector(selector)
            if text:
                self.navigation_history.append({
                    'action': 'click',
                    'text': text,
                    'timestamp': asyncio.get_event_loop().time()
                })

async def page_aware_fix_script(actions_file: str) -> str:
    """页面感知修复脚本的便捷函数"""
    async with PageAwareFixer() as fixer:
        return await fixer.fix_script_with_page_awareness(actions_file)

# 测试函数
async def test_page_aware_fixer():
    """测试页面感知修复器"""
    actions_file = 'output/供应商模块视频录制脚本_带时间戳_actions.json'
    fixed_file = await page_aware_fix_script(actions_file)
    print(f"🎉 测试完成，修复文件: {fixed_file}")

if __name__ == "__main__":
    asyncio.run(test_page_aware_fixer())
