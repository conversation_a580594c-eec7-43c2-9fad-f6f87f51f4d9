#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧食堂视频录制工具 - 启动器
用户可以选择使用图形界面或命令行模式
"""

import sys
import os

def show_menu():
    """显示启动菜单"""
    print("=" * 60)
    print("🎬 智慧食堂视频录制工具")
    print("=" * 60)
    print()
    print("请选择运行模式:")
    print("1. 图形界面模式 (推荐)")
    print("2. 命令行模式")
    print("3. 退出")
    print()

def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("请输入选择 (1-3): ").strip()
            
            if choice == "1":
                print("\n🚀 启动图形界面...")
                try:
                    import gui_app
                    gui_app.main()
                except ImportError as e:
                    print(f"❌ 无法启动图形界面: {e}")
                    print("请确保已安装 tkinter")
                except Exception as e:
                    print(f"❌ 启动图形界面时出错: {e}")
                break
                
            elif choice == "2":
                print("\n🚀 启动命令行模式...")
                try:
                    import main
                    main.main()
                except Exception as e:
                    print(f"❌ 启动命令行模式时出错: {e}")
                break
                
            elif choice == "3":
                print("\n👋 再见!")
                sys.exit(0)
                
            else:
                print("\n❌ 无效选择，请输入 1、2 或 3")
                input("按回车键继续...")
                
        except KeyboardInterrupt:
            print("\n\n👋 再见!")
            sys.exit(0)
        except Exception as e:
            print(f"\n❌ 出现错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
