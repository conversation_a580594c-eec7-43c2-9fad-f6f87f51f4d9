"""
财务凭证智能助手服务
基于新的工具类开发的智能助手功能，简化用户操作流程
"""

from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date
from flask import current_app
from flask_login import current_user

from app.utils.db_operations import FinancialDatabaseOperations, DatabaseOperations
from app.utils.error_decorators import financial_error_handler, FinancialValidators
from app.utils.response_helpers import FinancialResponseHelper, success
from app.models_financial import AccountingSubject
from app.models import StockIn, Supplier
from app import db


class FinancialAssistantService:
    """财务凭证智能助手服务"""
    
    @staticmethod
    @financial_error_handler(return_type='json')
    def suggest_voucher_template(business_type: str, amount: float, user_area) -> dict:
        """
        根据业务类型智能推荐凭证模板
        
        Args:
            business_type: 业务类型 ('purchase', 'sale', 'payment', 'receipt', 'expense')
            amount: 金额
            user_area: 用户所属区域
            
        Returns:
            推荐的凭证模板
        """
        templates = {
            'purchase': {
                'voucher_type': '采购凭证',
                'summary': '采购原材料',
                'details': [
                    {'subject_code': '1201', 'subject_name': '原材料', 'debit_amount': amount, 'credit_amount': 0},
                    {'subject_code': '2001', 'subject_name': '应付账款', 'debit_amount': 0, 'credit_amount': amount}
                ]
            },
            'payment': {
                'voucher_type': '付款凭证',
                'summary': '支付应付账款',
                'details': [
                    {'subject_code': '2001', 'subject_name': '应付账款', 'debit_amount': amount, 'credit_amount': 0},
                    {'subject_code': '1001', 'subject_name': '库存现金', 'debit_amount': 0, 'credit_amount': amount}
                ]
            },
            'expense': {
                'voucher_type': '费用凭证',
                'summary': '管理费用支出',
                'details': [
                    {'subject_code': '6601', 'subject_name': '管理费用', 'debit_amount': amount, 'credit_amount': 0},
                    {'subject_code': '1001', 'subject_name': '库存现金', 'debit_amount': 0, 'credit_amount': amount}
                ]
            },
            'sale': {
                'voucher_type': '销售凭证',
                'summary': '销售收入',
                'details': [
                    {'subject_code': '1122', 'subject_name': '应收账款', 'debit_amount': amount, 'credit_amount': 0},
                    {'subject_code': '6001', 'subject_name': '主营业务收入', 'debit_amount': 0, 'credit_amount': amount}
                ]
            },
            'receipt': {
                'voucher_type': '收款凭证',
                'summary': '收到货款',
                'details': [
                    {'subject_code': '1001', 'subject_name': '库存现金', 'debit_amount': amount, 'credit_amount': 0},
                    {'subject_code': '1122', 'subject_name': '应收账款', 'debit_amount': 0, 'credit_amount': amount}
                ]
            }
        }
        
        template = templates.get(business_type)
        if not template:
            raise ValueError(f"不支持的业务类型: {business_type}")
        
        # 验证会计科目是否存在
        for detail in template['details']:
            subject = AccountingSubject.query.filter(
                AccountingSubject.code == detail['subject_code'],
                db.or_(
                    AccountingSubject.is_system == 1,
                    AccountingSubject.area_id == user_area.id
                ),
                AccountingSubject.is_active == 1
            ).first()
            
            if subject:
                detail['subject_id'] = subject.id
                detail['subject_name'] = subject.name
            else:
                current_app.logger.warning(f"会计科目不存在: {detail['subject_code']}")
        
        return success("模板推荐成功", template)
    
    @staticmethod
    @financial_error_handler(return_type='json')
    def auto_generate_voucher_from_business(business_data: Dict[str, Any], user_area) -> dict:
        """
        根据业务数据自动生成凭证
        
        Args:
            business_data: 业务数据
            user_area: 用户所属区域
            
        Returns:
            生成的凭证信息
        """
        business_type = business_data.get('type')
        amount = float(business_data.get('amount', 0))
        description = business_data.get('description', '')
        business_date = business_data.get('date', date.today().strftime('%Y-%m-%d'))
        
        # 获取推荐模板
        template_result = FinancialAssistantService.suggest_voucher_template(
            business_type, amount, user_area
        )
        template = template_result.json['data']
        
        # 准备凭证数据
        voucher_data = {
            'voucher_date': business_date,
            'voucher_type': template['voucher_type'],
            'summary': description or template['summary'],
            'attachment_count': 0
        }
        
        # 准备明细数据
        details_data = []
        for i, detail in enumerate(template['details']):
            if 'subject_id' in detail:
                details_data.append({
                    'line_number': i + 1,
                    'subject_id': detail['subject_id'],
                    'summary': description or template['summary'],
                    'debit_amount': detail['debit_amount'],
                    'credit_amount': detail['credit_amount']
                })
        
        if not details_data:
            raise ValueError("无法生成凭证明细，请检查会计科目设置")
        
        # 创建凭证
        from app.utils.financial_voucher_service import FinancialVoucherService
        return FinancialVoucherService.create_voucher_with_details(
            voucher_data, details_data, user_area
        )
    
    @staticmethod
    @financial_error_handler(return_type='json')
    def batch_generate_vouchers_from_stock_ins(stock_in_ids: List[int], user_area) -> dict:
        """
        批量从入库单生成财务凭证
        
        Args:
            stock_in_ids: 入库单ID列表
            user_area: 用户所属区域
            
        Returns:
            批量生成结果
        """
        results = []
        errors = []
        
        for stock_in_id in stock_in_ids:
            try:
                from app.utils.financial_voucher_service import FinancialVoucherService
                result = FinancialVoucherService.create_voucher_from_stock_in(
                    stock_in_id, user_area, auto_review=True
                )
                results.append({
                    'stock_in_id': stock_in_id,
                    'success': True,
                    'voucher_id': result.json['voucher_id'],
                    'voucher_number': result.json['voucher_number']
                })
            except Exception as e:
                errors.append({
                    'stock_in_id': stock_in_id,
                    'success': False,
                    'error': str(e)
                })
                current_app.logger.error(f"批量生成凭证失败 - 入库单ID: {stock_in_id}, 错误: {str(e)}")
        
        return success("批量生成完成", {
            'total': len(stock_in_ids),
            'success_count': len(results),
            'error_count': len(errors),
            'results': results,
            'errors': errors
        })
    
    @staticmethod
    @financial_error_handler(return_type='json')
    def validate_and_suggest_corrections(voucher_data: Dict[str, Any], 
                                       details_data: List[Dict[str, Any]]) -> dict:
        """
        验证凭证数据并提供修正建议
        
        Args:
            voucher_data: 凭证数据
            details_data: 明细数据
            
        Returns:
            验证结果和修正建议
        """
        suggestions = []
        warnings = []
        
        # 验证基本数据
        is_valid, error_msg = FinancialValidators.validate_voucher_data(voucher_data)
        if not is_valid:
            suggestions.append({
                'type': 'error',
                'field': 'voucher_data',
                'message': error_msg,
                'suggestion': '请检查并修正凭证基本信息'
            })
        
        # 验证明细数据
        is_valid, error_msg = FinancialValidators.validate_voucher_details(details_data)
        if not is_valid:
            suggestions.append({
                'type': 'error',
                'field': 'details_data',
                'message': error_msg,
                'suggestion': '请检查并修正凭证明细信息'
            })
        
        # 检查借贷平衡
        total_debit = sum(float(detail.get('debit_amount', 0)) for detail in details_data)
        total_credit = sum(float(detail.get('credit_amount', 0)) for detail in details_data)
        
        if abs(total_debit - total_credit) > 0.01:
            difference = abs(total_debit - total_credit)
            suggestions.append({
                'type': 'error',
                'field': 'balance',
                'message': f'借贷不平衡，差额: {difference}',
                'suggestion': f'请调整明细金额，使借方总额({total_debit})等于贷方总额({total_credit})'
            })
        
        # 检查科目合理性
        for i, detail in enumerate(details_data):
            subject_id = detail.get('subject_id')
            debit_amount = float(detail.get('debit_amount', 0))
            credit_amount = float(detail.get('credit_amount', 0))
            
            if subject_id:
                subject = AccountingSubject.query.get(subject_id)
                if subject:
                    # 检查科目方向
                    if subject.balance_direction == '借' and credit_amount > debit_amount:
                        warnings.append({
                            'type': 'warning',
                            'field': f'detail_{i}',
                            'message': f'科目"{subject.name}"通常为借方余额，但贷方金额较大',
                            'suggestion': '请确认科目使用是否正确'
                        })
                    elif subject.balance_direction == '贷' and debit_amount > credit_amount:
                        warnings.append({
                            'type': 'warning',
                            'field': f'detail_{i}',
                            'message': f'科目"{subject.name}"通常为贷方余额，但借方金额较大',
                            'suggestion': '请确认科目使用是否正确'
                        })
        
        # 检查摘要一致性
        summaries = [detail.get('summary', '') for detail in details_data]
        if len(set(summaries)) > 1:
            warnings.append({
                'type': 'warning',
                'field': 'summary',
                'message': '明细摘要不一致',
                'suggestion': '建议统一明细摘要，保持凭证的一致性'
            })
        
        return success("验证完成", {
            'is_valid': len([s for s in suggestions if s['type'] == 'error']) == 0,
            'suggestions': suggestions,
            'warnings': warnings,
            'summary': {
                'total_debit': total_debit,
                'total_credit': total_credit,
                'is_balanced': abs(total_debit - total_credit) <= 0.01,
                'detail_count': len(details_data)
            }
        })
    
    @staticmethod
    @financial_error_handler(return_type='json')
    def get_frequent_subjects(user_area, limit: int = 10) -> dict:
        """
        获取常用会计科目
        
        Args:
            user_area: 用户所属区域
            limit: 返回数量限制
            
        Returns:
            常用科目列表
        """
        # 查询最近30天使用频率最高的科目
        sql = """
            SELECT s.id, s.code, s.name, s.subject_type, COUNT(*) as usage_count
            FROM voucher_details vd
            INNER JOIN accounting_subjects s ON vd.subject_id = s.id
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = :area_id
            AND fv.voucher_date >= DATEADD(day, -30, GETDATE())
            AND fv.status IN ('已审核', '已记账')
            GROUP BY s.id, s.code, s.name, s.subject_type
            ORDER BY usage_count DESC, s.code
        """
        
        results = DatabaseOperations.execute_select(sql, {
            'area_id': user_area.id
        })
        
        frequent_subjects = []
        for row in results[:limit]:
            frequent_subjects.append({
                'id': row[0],
                'code': row[1],
                'name': row[2],
                'subject_type': row[3],
                'usage_count': row[4]
            })
        
        return success("获取常用科目成功", frequent_subjects)
    
    @staticmethod
    @financial_error_handler(return_type='json')
    def get_voucher_statistics(user_area, period: str = 'month') -> dict:
        """
        获取凭证统计信息
        
        Args:
            user_area: 用户所属区域
            period: 统计周期 ('week', 'month', 'quarter', 'year')
            
        Returns:
            统计信息
        """
        period_map = {
            'week': 7,
            'month': 30,
            'quarter': 90,
            'year': 365
        }
        
        days = period_map.get(period, 30)
        
        sql = """
            SELECT 
                COUNT(*) as total_vouchers,
                SUM(total_amount) as total_amount,
                COUNT(CASE WHEN status = '草稿' THEN 1 END) as draft_count,
                COUNT(CASE WHEN status = '待审核' THEN 1 END) as pending_count,
                COUNT(CASE WHEN status = '已审核' THEN 1 END) as approved_count,
                COUNT(CASE WHEN status = '已记账' THEN 1 END) as posted_count
            FROM financial_vouchers
            WHERE area_id = :area_id
            AND voucher_date >= DATEADD(day, -:days, GETDATE())
        """
        
        result = DatabaseOperations.execute_select(sql, {
            'area_id': user_area.id,
            'days': days
        })
        
        if result:
            row = result[0]
            statistics = {
                'period': period,
                'total_vouchers': row[0] or 0,
                'total_amount': float(row[1] or 0),
                'status_breakdown': {
                    'draft': row[2] or 0,
                    'pending': row[3] or 0,
                    'approved': row[4] or 0,
                    'posted': row[5] or 0
                }
            }
        else:
            statistics = {
                'period': period,
                'total_vouchers': 0,
                'total_amount': 0,
                'status_breakdown': {
                    'draft': 0,
                    'pending': 0,
                    'approved': 0,
                    'posted': 0
                }
            }
        
        return success("获取统计信息成功", statistics)
