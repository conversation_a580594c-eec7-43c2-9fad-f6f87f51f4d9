"""
财务凭证服务类
使用新的工具类重构财务凭证相关功能，提供更安全、更易维护的代码
"""

from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date
from flask import current_app
from flask_login import current_user

from app.utils.db_operations import FinancialDatabaseOperations, DatabaseOperationError
from app.utils.error_decorators import financial_error_handler, FinancialValidators
from app.utils.response_helpers import FinancialResponseHelper
from app.models_financial import AccountingSubject
from app.models import StockIn
from app import db


class FinancialVoucherService:
    """财务凭证服务类"""
    
    @staticmethod
    @financial_error_handler(return_type='json')
    def create_voucher_with_details(voucher_data: Dict[str, Any], 
                                  details_data: List[Dict[str, Any]],
                                  user_area) -> dict:
        """
        创建财务凭证及其明细
        
        Args:
            voucher_data: 凭证基本数据
            details_data: 凭证明细数据列表
            user_area: 用户所属区域
            
        Returns:
            JSON响应
        """
        # 数据验证
        is_valid, error_msg = FinancialValidators.validate_voucher_data(voucher_data)
        if not is_valid:
            raise ValueError(error_msg)
        
        is_valid, error_msg = FinancialValidators.validate_voucher_details(details_data)
        if not is_valid:
            raise ValueError(error_msg)
        
        # 生成凭证号
        voucher_date = datetime.strptime(voucher_data['voucher_date'], '%Y-%m-%d').date()
        voucher_number = FinancialDatabaseOperations.generate_voucher_number(
            user_area.id, voucher_date
        )
        
        # 准备凭证数据
        voucher_data.update({
            'voucher_number': voucher_number,
            'area_id': user_area.id,
            'created_by': current_user.id,
            'status': '草稿'
        })
        
        # 计算总金额
        total_debit = sum(float(detail.get('debit_amount', 0)) for detail in details_data)
        voucher_data['total_amount'] = total_debit
        
        # 创建凭证和明细
        voucher_id = FinancialDatabaseOperations.create_voucher_with_details(
            voucher_data, details_data
        )
        
        current_app.logger.info(f"财务凭证创建成功: ID={voucher_id}, 凭证号={voucher_number}")
        
        return FinancialResponseHelper.voucher_response(
            voucher_id, voucher_number, "凭证创建成功"
        )
    
    @staticmethod
    @financial_error_handler(return_type='json')
    def create_voucher_from_stock_in(stock_in_id: int, user_area, auto_review: bool = True) -> dict:
        """
        从入库单创建财务凭证
        
        Args:
            stock_in_id: 入库单ID
            user_area: 用户所属区域
            auto_review: 是否自动审核
            
        Returns:
            JSON响应
        """
        # 获取入库单信息
        stock_in = StockIn.query.filter_by(
            id=stock_in_id,
            area_id=user_area.id
        ).first()
        
        if not stock_in:
            raise ValueError("入库单不存在")
        
        if stock_in.voucher_id:
            raise ValueError("该入库单已生成财务凭证")
        
        # 获取会计科目
        inventory_subject, payable_subject = FinancialVoucherService._get_accounting_subjects(user_area.id)
        
        # 准备凭证数据
        voucher_data = {
            'voucher_date': stock_in.stock_in_date.strftime('%Y-%m-%d'),
            'voucher_type': '入库凭证',
            'summary': f'入库单{stock_in.stock_in_number}',
            'source_type': '入库单',
            'source_id': stock_in_id,
            'attachment_count': 0
        }
        
        # 准备明细数据
        details_data = [
            {
                'line_number': 1,
                'subject_id': inventory_subject.id,
                'summary': f'入库单{stock_in.stock_in_number}',
                'debit_amount': float(stock_in.total_cost),
                'credit_amount': 0.0
            },
            {
                'line_number': 2,
                'subject_id': payable_subject.id,
                'summary': f'入库单{stock_in.stock_in_number}',
                'debit_amount': 0.0,
                'credit_amount': float(stock_in.total_cost)
            }
        ]
        
        # 创建凭证
        result = FinancialVoucherService.create_voucher_with_details(
            voucher_data, details_data, user_area
        )
        
        # 更新入库单关联
        voucher_id = result.json['voucher_id']
        FinancialDatabaseOperations.execute_update(
            'stock_ins',
            {'voucher_id': voucher_id},
            {'id': stock_in_id}
        )
        
        # 自动审核
        if auto_review:
            FinancialVoucherService.review_voucher(voucher_id, user_area)
        
        return result
    
    @staticmethod
    @financial_error_handler(return_type='json')
    def review_voucher(voucher_id: int, user_area) -> dict:
        """
        审核财务凭证
        
        Args:
            voucher_id: 凭证ID
            user_area: 用户所属区域
            
        Returns:
            JSON响应
        """
        # 验证凭证借贷平衡
        is_balanced, error_msg = FinancialDatabaseOperations.validate_voucher_balance(voucher_id)
        if not is_balanced:
            raise ValueError(error_msg)
        
        # 更新凭证状态
        update_data = {
            'status': '已审核',
            'reviewed_by': current_user.id,
            'reviewed_at': datetime.now().replace(microsecond=0)
        }
        
        rows_affected = FinancialDatabaseOperations.execute_update(
            'financial_vouchers',
            update_data,
            {'id': voucher_id, 'area_id': user_area.id}
        )
        
        if rows_affected == 0:
            raise ValueError("凭证不存在或无权限操作")
        
        current_app.logger.info(f"财务凭证审核成功: ID={voucher_id}")
        
        return FinancialResponseHelper.success_response("凭证审核成功")
    
    @staticmethod
    @financial_error_handler(return_type='json')
    def validate_voucher_balance(voucher_id: int) -> dict:
        """
        验证凭证借贷平衡
        
        Args:
            voucher_id: 凭证ID
            
        Returns:
            JSON响应
        """
        # 查询凭证明细
        details_sql = """
            SELECT debit_amount, credit_amount
            FROM voucher_details
            WHERE voucher_id = :voucher_id
        """
        
        details = FinancialDatabaseOperations.execute_select(
            details_sql, {'voucher_id': voucher_id}
        )
        
        if not details:
            raise ValueError("凭证没有明细")
        
        # 计算借贷总额
        total_debit = sum(float(detail[0] or 0) for detail in details)
        total_credit = sum(float(detail[1] or 0) for detail in details)
        
        is_balanced = abs(total_debit - total_credit) <= 0.01
        
        return FinancialResponseHelper.balance_validation_response(
            is_balanced, total_debit, total_credit
        )
    
    @staticmethod
    def _get_accounting_subjects(area_id: int) -> Tuple[AccountingSubject, AccountingSubject]:
        """
        获取必要的会计科目
        
        Args:
            area_id: 区域ID
            
        Returns:
            (原材料科目, 应付账款科目)
            
        Raises:
            ValueError: 科目不存在
        """
        # 查询原材料科目 (1201)
        inventory_subject = AccountingSubject.query.filter(
            AccountingSubject.code == '1201',
            db.or_(
                AccountingSubject.is_system == 1,
                AccountingSubject.area_id == area_id
            ),
            AccountingSubject.is_active == 1
        ).first()
        
        if not inventory_subject:
            raise ValueError("未找到原材料科目(1201)，请联系管理员初始化系统科目")
        
        # 查询应付账款科目 (2001)
        payable_subject = AccountingSubject.query.filter(
            AccountingSubject.code == '2001',
            db.or_(
                AccountingSubject.is_system == 1,
                AccountingSubject.area_id == area_id
            ),
            AccountingSubject.is_active == 1
        ).first()
        
        if not payable_subject:
            raise ValueError("未找到应付账款科目(2001)，请联系管理员初始化系统科目")
        
        return inventory_subject, payable_subject


class PayableService:
    """应付账款服务类"""
    
    @staticmethod
    @financial_error_handler(return_type='json')
    def create_payable_from_stock_in(stock_in_id: int, user_area) -> dict:
        """
        从入库单创建应付账款
        
        Args:
            stock_in_id: 入库单ID
            user_area: 用户所属区域
            
        Returns:
            JSON响应
        """
        # 获取入库单信息
        stock_in = StockIn.query.filter_by(
            id=stock_in_id,
            area_id=user_area.id
        ).first()
        
        if not stock_in:
            raise ValueError("入库单不存在")
        
        if stock_in.payable_id:
            raise ValueError("该入库单已生成应付账款")
        
        # 生成应付账款编号
        today = date.today()
        prefix = f"YF{today.strftime('%Y%m%d')}"
        payable_number = FinancialDatabaseOperations.get_next_sequence_number(
            'account_payables', 'payable_number', prefix, user_area.id
        )
        
        # 准备应付账款数据
        payable_data = {
            'payable_number': payable_number,
            'area_id': user_area.id,
            'supplier_id': stock_in.supplier_id,
            'stock_in_id': stock_in_id,
            'purchase_order_id': stock_in.purchase_order_id,
            'original_amount': float(stock_in.total_cost),
            'paid_amount': 0.0,
            'balance_amount': float(stock_in.total_cost),
            'status': '未付款',
            'created_by': current_user.id
        }
        
        # 创建应付账款
        payable_id = FinancialDatabaseOperations.execute_insert(
            'account_payables', payable_data
        )
        
        # 创建财务凭证
        voucher_result = FinancialVoucherService.create_voucher_from_stock_in(
            stock_in_id, user_area, auto_review=True
        )
        
        # 更新入库单关联
        FinancialDatabaseOperations.execute_update(
            'stock_ins',
            {'payable_id': payable_id},
            {'id': stock_in_id}
        )
        
        current_app.logger.info(f"应付账款创建成功: ID={payable_id}, 编号={payable_number}")
        
        return FinancialResponseHelper.success_response(
            "应付账款创建成功",
            {
                'payable_id': payable_id,
                'payable_number': payable_number,
                'voucher_info': voucher_result.json
            }
        )
