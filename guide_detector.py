#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能引导检测和跳过模块
自动检测页面上的引导界面并智能跳过
"""

import asyncio
from typing import List, Dict, Optional

class GuideDetector:
    """智能引导检测器"""
    
    def __init__(self, page):
        self.page = page
        
    async def detect_and_skip_guides(self, max_attempts: int = 5) -> Dict:
        """检测并跳过所有引导界面"""
        result = {
            'guides_found': 0,
            'guides_skipped': 0,
            'attempts': 0,
            'final_state': 'unknown',
            'actions_taken': []
        }
        
        print("🔍 开始智能引导检测...")
        
        for attempt in range(max_attempts):
            result['attempts'] = attempt + 1
            print(f"🔄 第 {attempt + 1} 次检测引导界面...")
            
            # 等待页面稳定
            await self.page.wait_for_timeout(2000)
            
            # 检测引导界面
            guide_info = await self._detect_guide_elements()
            
            if not guide_info['has_guide']:
                print("✅ 未检测到引导界面，页面可正常操作")
                result['final_state'] = 'ready'
                break
            
            result['guides_found'] += 1
            print(f"🎯 检测到引导界面: {guide_info['type']}")
            
            # 尝试跳过引导
            skip_success = await self._skip_guide(guide_info)
            
            if skip_success:
                result['guides_skipped'] += 1
                result['actions_taken'].append(f"跳过{guide_info['type']}")
                print(f"✅ 成功跳过引导: {guide_info['type']}")
            else:
                print(f"❌ 跳过引导失败: {guide_info['type']}")
                # 尝试其他方法
                await self._force_skip_guide()
                result['actions_taken'].append("强制跳过尝试")
        
        # 最终验证
        final_check = await self._verify_page_ready()
        result['final_state'] = 'ready' if final_check else 'blocked'
        
        print(f"📊 引导检测完成: 发现 {result['guides_found']} 个，跳过 {result['guides_skipped']} 个")
        return result
    
    async def _detect_guide_elements(self) -> Dict:
        """检测页面上的引导元素"""
        guide_info = {
            'has_guide': False,
            'type': 'none',
            'elements': [],
            'skip_selectors': []
        }
        
        # 检测各种类型的引导界面
        guide_patterns = [
            {
                'name': '新手引导',
                'selectors': [
                    '.intro-js-overlay',
                    '.introjs-overlay',
                    '[class*="intro"]',
                    '[class*="guide"]',
                    '[class*="tour"]',
                    '[class*="tutorial"]'
                ],
                'skip_selectors': [
                    '.introjs-skipbutton',
                    '.intro-js-skipbutton',
                    'text=跳过',
                    'text=Skip',
                    'button:has-text("跳过")',
                    'button:has-text("Skip")'
                ]
            },
            {
                'name': '模态对话框',
                'selectors': [
                    '.modal',
                    '.dialog',
                    '.popup',
                    '[role="dialog"]',
                    '[class*="modal"]',
                    '[class*="dialog"]'
                ],
                'skip_selectors': [
                    '.modal-close',
                    '.dialog-close',
                    'button:has-text("关闭")',
                    'button:has-text("Close")',
                    'text=×',
                    '[aria-label="关闭"]',
                    '[aria-label="Close"]'
                ]
            },
            {
                'name': '遮罩层',
                'selectors': [
                    '.overlay',
                    '.mask',
                    '[class*="overlay"]',
                    '[class*="mask"]',
                    '[style*="position: fixed"]'
                ],
                'skip_selectors': [
                    'text=跳过',
                    'text=关闭',
                    'text=确定',
                    'text=OK',
                    'button',
                    '[role="button"]'
                ]
            }
        ]
        
        for pattern in guide_patterns:
            for selector in pattern['selectors']:
                try:
                    elements = await self.page.query_selector_all(selector)
                    visible_elements = []
                    
                    for element in elements:
                        if await element.is_visible():
                            visible_elements.append(element)
                    
                    if visible_elements:
                        guide_info['has_guide'] = True
                        guide_info['type'] = pattern['name']
                        guide_info['elements'] = visible_elements
                        guide_info['skip_selectors'] = pattern['skip_selectors']
                        print(f"🎯 发现 {pattern['name']}: {len(visible_elements)} 个元素")
                        return guide_info
                        
                except Exception as e:
                    continue
        
        return guide_info
    
    async def _skip_guide(self, guide_info: Dict) -> bool:
        """尝试跳过指定的引导界面"""
        skip_selectors = guide_info.get('skip_selectors', [])
        
        # 添加通用跳过选择器
        universal_selectors = [
            'text=跳过',
            'text=Skip',
            'text=关闭',
            'text=Close',
            'text=确定',
            'text=OK',
            'text=下一步',
            'text=Next',
            'text=完成',
            'text=Done',
            'button:has-text("跳过")',
            'button:has-text("关闭")',
            'button:has-text("确定")',
            '[class*="skip"]',
            '[class*="close"]',
            '[class*="next"]',
            '[class*="done"]',
            '[aria-label*="跳过"]',
            '[aria-label*="关闭"]',
            '[aria-label*="Close"]',
            '[aria-label*="Skip"]'
        ]
        
        all_selectors = skip_selectors + universal_selectors
        
        for selector in all_selectors:
            try:
                # 等待元素出现（短时间）
                await self.page.wait_for_selector(selector, timeout=1000)
                
                # 检查元素是否可见和可点击
                element = await self.page.query_selector(selector)
                if element and await element.is_visible():
                    await element.click()
                    print(f"✅ 成功点击跳过按钮: {selector}")
                    
                    # 等待引导消失
                    await self.page.wait_for_timeout(1000)
                    return True
                    
            except Exception as e:
                continue
        
        return False
    
    async def _force_skip_guide(self):
        """强制跳过引导的备选方案"""
        print("🔧 尝试强制跳过引导...")
        
        # 方法1: 按ESC键
        try:
            await self.page.keyboard.press('Escape')
            await self.page.wait_for_timeout(500)
            print("✅ 尝试ESC键跳过")
        except:
            pass
        
        # 方法2: 点击页面空白区域
        try:
            await self.page.click('body', position={'x': 10, 'y': 10})
            await self.page.wait_for_timeout(500)
            print("✅ 尝试点击空白区域")
        except:
            pass
        
        # 方法3: 按Enter键
        try:
            await self.page.keyboard.press('Enter')
            await self.page.wait_for_timeout(500)
            print("✅ 尝试Enter键跳过")
        except:
            pass
        
        # 方法4: 按Tab键然后Enter
        try:
            await self.page.keyboard.press('Tab')
            await self.page.keyboard.press('Enter')
            await self.page.wait_for_timeout(500)
            print("✅ 尝试Tab+Enter跳过")
        except:
            pass
    
    async def _verify_page_ready(self) -> bool:
        """验证页面是否准备就绪，可以进行正常操作"""
        try:
            # 检查是否还有引导元素
            guide_check = await self._detect_guide_elements()
            if guide_check['has_guide']:
                print("⚠️ 页面仍有引导元素")
                return False
            
            # 检查左侧菜单是否可用
            menu_selectors = [
                '.sidebar',
                '.menu',
                '.nav',
                '[class*="sidebar"]',
                '[class*="menu"]',
                '[class*="nav"]',
                'nav',
                'aside'
            ]
            
            for selector in menu_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    for element in elements:
                        if await element.is_visible():
                            print(f"✅ 发现可用菜单: {selector}")
                            return True
                except:
                    continue
            
            # 检查页面是否有可交互元素
            interactive_elements = await self.page.query_selector_all('button, a, input, [role="button"]')
            visible_count = 0
            
            for element in interactive_elements:
                if await element.is_visible():
                    visible_count += 1
            
            if visible_count > 5:  # 如果有足够多的可交互元素
                print(f"✅ 页面有 {visible_count} 个可交互元素，应该可以正常操作")
                return True
            
            print("⚠️ 页面可交互元素较少，可能仍被阻塞")
            return False
            
        except Exception as e:
            print(f"❌ 页面状态检查失败: {e}")
            return False

async def smart_skip_guides(page, max_attempts: int = 5) -> Dict:
    """智能跳过引导的便捷函数"""
    detector = GuideDetector(page)
    return await detector.detect_and_skip_guides(max_attempts)

# 测试函数
async def test_guide_detection():
    """测试引导检测功能"""
    from playwright.async_api import async_playwright
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        # 访问测试页面
        await page.goto("http://xiaoyuanst.com")
        await page.click("text=体验系统")
        
        # 智能跳过引导
        result = await smart_skip_guides(page)
        
        print("🎉 测试完成")
        print(f"结果: {result}")
        
        await browser.close()

if __name__ == "__main__":
    asyncio.run(test_guide_detection())
