[{"step": 1, "narration": "首先我们登录智慧食堂平台。我使用游客账号登录，这样您也可以跟着一起操作体验。登录成功后，我们可以看到系统的主界面，显示当前登录用户为guest_demo。", "audio": "06-recipe-library-demo_step1.mp3", "action": "goto", "selector": "", "value": "xiaoyuanst.com"}, {"step": 2, "narration": "首先我们登录智慧食堂平台。我使用游客账号登录，这样您也可以跟着一起操作体验。登录成功后，我们可以看到系统的主界面，显示当前登录用户为guest_demo。", "audio": "06-recipe-library-demo_step2.mp3", "action": "click", "selector": "text=体"}, {"step": 3, "narration": "现在我们进入食谱库管理模块。点击顶部的'菜单规划'菜单，然后选择'食谱库'。食谱库是菜单规划的核心组成部分，它存储了所有的菜品配方信息，为周菜单制定、消耗计划生成、营养分析等功能提供基础数据支撑。", "audio": "06-recipe-library-demo_step3.mp3", "action": "click", "selector": "text=顶"}, {"step": 4, "narration": "现在我们进入食谱库管理模块。点击顶部的'菜单规划'菜单，然后选择'食谱库'。食谱库是菜单规划的核心组成部分，它存储了所有的菜品配方信息，为周菜单制定、消耗计划生成、营养分析等功能提供基础数据支撑。", "audio": "06-recipe-library-demo_step4.mp3", "action": "click", "selector": "text=食"}]