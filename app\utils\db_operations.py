"""
数据库操作工具类
提供安全、统一的数据库操作方法，消除代码重复并防止SQL注入
"""

from sqlalchemy import text
from flask import current_app
from app import db
from datetime import datetime
from typing import Dict, List, Any, Optional, Union, Tuple
import logging


class DatabaseOperations:
    """数据库操作工具类"""
    
    @staticmethod
    def execute_insert(table_name: str, data: Dict[str, Any], return_id: bool = True) -> Optional[int]:
        """
        安全的插入操作
        
        Args:
            table_name: 表名
            data: 插入数据字典
            return_id: 是否返回插入记录的ID
            
        Returns:
            插入记录的ID（如果return_id为True）
            
        Raises:
            DatabaseOperationError: 数据库操作失败
        """
        try:
            # 构建参数化查询
            columns = ', '.join(data.keys())
            placeholders = ', '.join([f':{key}' for key in data.keys()])
            
            sql = text(f"""
                INSERT INTO {table_name} ({columns})
                VALUES ({placeholders})
            """)
            
            result = db.session.execute(sql, data)
            
            if return_id:
                # 获取插入记录的ID
                id_sql = text(f"""
                    SELECT id FROM {table_name}
                    WHERE id = (SELECT SCOPE_IDENTITY())
                """)
                id_result = db.session.execute(id_sql).fetchone()
                return id_result[0] if id_result else None
                
            return None
            
        except Exception as e:
            current_app.logger.error(f"插入操作失败 - 表: {table_name}, 错误: {str(e)}")
            raise DatabaseOperationError(f"插入数据失败: {str(e)}")
    
    @staticmethod
    def execute_update(table_name: str, data: Dict[str, Any], conditions: Dict[str, Any]) -> int:
        """
        安全的更新操作
        
        Args:
            table_name: 表名
            data: 更新数据字典
            conditions: 更新条件字典
            
        Returns:
            受影响的行数
            
        Raises:
            DatabaseOperationError: 数据库操作失败
        """
        try:
            # 构建SET子句
            set_clause = ', '.join([f'{key} = :{key}' for key in data.keys()])
            
            # 构建WHERE子句
            where_clause = ' AND '.join([f'{key} = :where_{key}' for key in conditions.keys()])
            
            # 合并参数
            params = data.copy()
            for key, value in conditions.items():
                params[f'where_{key}'] = value
            
            sql = text(f"""
                UPDATE {table_name}
                SET {set_clause}
                WHERE {where_clause}
            """)
            
            result = db.session.execute(sql, params)
            return result.rowcount
            
        except Exception as e:
            current_app.logger.error(f"更新操作失败 - 表: {table_name}, 错误: {str(e)}")
            raise DatabaseOperationError(f"更新数据失败: {str(e)}")
    
    @staticmethod
    def execute_delete(table_name: str, conditions: Dict[str, Any]) -> int:
        """
        安全的删除操作
        
        Args:
            table_name: 表名
            conditions: 删除条件字典
            
        Returns:
            受影响的行数
            
        Raises:
            DatabaseOperationError: 数据库操作失败
        """
        try:
            # 构建WHERE子句
            where_clause = ' AND '.join([f'{key} = :{key}' for key in conditions.keys()])
            
            sql = text(f"""
                DELETE FROM {table_name}
                WHERE {where_clause}
            """)
            
            result = db.session.execute(sql, conditions)
            return result.rowcount
            
        except Exception as e:
            current_app.logger.error(f"删除操作失败 - 表: {table_name}, 错误: {str(e)}")
            raise DatabaseOperationError(f"删除数据失败: {str(e)}")
    
    @staticmethod
    def execute_select(sql: str, params: Dict[str, Any] = None) -> List[Any]:
        """
        安全的查询操作
        
        Args:
            sql: SQL查询语句（使用参数占位符）
            params: 查询参数字典
            
        Returns:
            查询结果列表
            
        Raises:
            DatabaseOperationError: 数据库操作失败
        """
        try:
            if params is None:
                params = {}
                
            result = db.session.execute(text(sql), params)
            return result.fetchall()
            
        except Exception as e:
            current_app.logger.error(f"查询操作失败 - SQL: {sql[:100]}..., 错误: {str(e)}")
            raise DatabaseOperationError(f"查询数据失败: {str(e)}")
    
    @staticmethod
    def handle_transaction(operations: List[callable]) -> Any:
        """
        事务处理包装器
        
        Args:
            operations: 操作函数列表
            
        Returns:
            最后一个操作的返回值
            
        Raises:
            DatabaseOperationError: 事务执行失败
        """
        try:
            results = []
            for operation in operations:
                result = operation()
                results.append(result)
            
            db.session.commit()
            current_app.logger.info(f"事务执行成功，包含 {len(operations)} 个操作")
            
            return results[-1] if results else None
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"事务执行失败: {str(e)}")
            raise DatabaseOperationError(f"事务执行失败: {str(e)}")
    
    @staticmethod
    def get_next_sequence_number(table_name: str, field_name: str, 
                                prefix: str = '', area_id: int = None) -> str:
        """
        获取下一个序列号
        
        Args:
            table_name: 表名
            field_name: 序列号字段名
            prefix: 序列号前缀
            area_id: 区域ID（用于学校级隔离）
            
        Returns:
            下一个序列号
        """
        try:
            # 构建查询条件
            where_conditions = []
            params = {}
            
            if area_id is not None:
                where_conditions.append("area_id = :area_id")
                params['area_id'] = area_id
            
            if prefix:
                where_conditions.append(f"{field_name} LIKE :prefix")
                params['prefix'] = f'{prefix}%'
            
            where_clause = ' AND '.join(where_conditions) if where_conditions else '1=1'
            
            sql = text(f"""
                SELECT MAX(CAST(
                    CASE 
                        WHEN {field_name} LIKE :prefix_pattern
                        THEN SUBSTRING({field_name}, {len(prefix) + 1}, LEN({field_name}))
                        ELSE '0'
                    END AS INT
                )) as max_num
                FROM {table_name}
                WHERE {where_clause}
            """)
            
            params['prefix_pattern'] = f'{prefix}%'
            result = db.session.execute(sql, params).fetchone()
            
            max_num = result[0] if result and result[0] else 0
            next_num = max_num + 1
            
            return f'{prefix}{next_num:04d}'
            
        except Exception as e:
            current_app.logger.error(f"获取序列号失败: {str(e)}")
            raise DatabaseOperationError(f"获取序列号失败: {str(e)}")
    
    @staticmethod
    def check_record_exists(table_name: str, conditions: Dict[str, Any]) -> bool:
        """
        检查记录是否存在
        
        Args:
            table_name: 表名
            conditions: 检查条件字典
            
        Returns:
            记录是否存在
        """
        try:
            where_clause = ' AND '.join([f'{key} = :{key}' for key in conditions.keys()])
            
            sql = text(f"""
                SELECT COUNT(1) as count
                FROM {table_name}
                WHERE {where_clause}
            """)
            
            result = db.session.execute(sql, conditions).fetchone()
            return result[0] > 0 if result else False
            
        except Exception as e:
            current_app.logger.error(f"检查记录存在性失败: {str(e)}")
            return False


class DatabaseOperationError(Exception):
    """数据库操作异常"""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)


class FinancialDatabaseOperations(DatabaseOperations):
    """财务模块专用数据库操作类"""
    
    @staticmethod
    def create_voucher_with_details(voucher_data: Dict[str, Any], 
                                  details_data: List[Dict[str, Any]]) -> int:
        """
        创建财务凭证及其明细（事务操作）
        
        Args:
            voucher_data: 凭证数据
            details_data: 明细数据列表
            
        Returns:
            凭证ID
        """
        def create_voucher():
            return DatabaseOperations.execute_insert('financial_vouchers', voucher_data)
        
        def create_details():
            voucher_id = create_voucher()
            for detail in details_data:
                detail['voucher_id'] = voucher_id
                DatabaseOperations.execute_insert('voucher_details', detail, return_id=False)
            return voucher_id
        
        return DatabaseOperations.handle_transaction([create_details])
    
    @staticmethod
    def generate_voucher_number(area_id: int, voucher_date: datetime = None) -> str:
        """
        生成财务凭证号
        
        Args:
            area_id: 学校ID
            voucher_date: 凭证日期
            
        Returns:
            凭证号
        """
        if voucher_date is None:
            voucher_date = datetime.now()
        
        # 按月生成凭证号：YYYYMM-0001
        prefix = voucher_date.strftime('%Y%m-')
        
        return DatabaseOperations.get_next_sequence_number(
            'financial_vouchers', 
            'voucher_number', 
            prefix, 
            area_id
        )
    
    @staticmethod
    def validate_voucher_balance(voucher_id: int) -> Tuple[bool, str]:
        """
        验证凭证借贷平衡
        
        Args:
            voucher_id: 凭证ID
            
        Returns:
            (是否平衡, 错误信息)
        """
        try:
            sql = text("""
                SELECT 
                    SUM(debit_amount) as total_debit,
                    SUM(credit_amount) as total_credit
                FROM voucher_details
                WHERE voucher_id = :voucher_id
            """)
            
            result = db.session.execute(sql, {'voucher_id': voucher_id}).fetchone()
            
            if not result:
                return False, "凭证没有明细"
            
            total_debit = float(result[0] or 0)
            total_credit = float(result[1] or 0)
            
            if abs(total_debit - total_credit) > 0.01:  # 允许0.01的误差
                return False, f"借贷不平衡：借方 {total_debit}，贷方 {total_credit}"
            
            return True, ""
            
        except Exception as e:
            current_app.logger.error(f"验证凭证平衡失败: {str(e)}")
            return False, f"验证失败: {str(e)}"
