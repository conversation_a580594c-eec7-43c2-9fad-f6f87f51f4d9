#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复音频文件问题的脚本
清理损坏的音频文件并重新生成
"""

import os
import glob
import asyncio
import edge_tts

def clean_corrupted_audio():
    """清理损坏的音频文件"""
    print("🔍 检查音频文件...")
    
    mp3_files = glob.glob("output/*.mp3")
    corrupted_files = []
    
    for mp3_file in mp3_files:
        try:
            file_size = os.path.getsize(mp3_file)
            if file_size < 1024:  # 小于1KB的文件可能损坏
                corrupted_files.append(mp3_file)
                print(f"❌ 发现损坏文件: {mp3_file} (大小: {file_size} bytes)")
        except Exception as e:
            print(f"❌ 检查文件时出错 {mp3_file}: {e}")
            corrupted_files.append(mp3_file)
    
    if corrupted_files:
        print(f"\n🗑️ 清理 {len(corrupted_files)} 个损坏的音频文件...")
        for file in corrupted_files:
            try:
                os.remove(file)
                print(f"✅ 已删除: {file}")
            except Exception as e:
                print(f"❌ 删除失败 {file}: {e}")
    else:
        print("✅ 未发现损坏的音频文件")

async def regenerate_audio():
    """重新生成音频文件"""
    print("\n🎵 重新生成音频文件...")
    
    # 导入主程序模块
    import main
    
    # 获取所有脚本文件
    script_files = glob.glob("video-scripts/*.md")
    script_files = [f for f in script_files if not f.endswith("README.md")]
    
    for script_file in script_files:
        base_name = os.path.splitext(os.path.basename(script_file))[0]
        print(f"\n📝 处理脚本: {base_name}")
        
        try:
            # 解析步骤
            steps = main.extract_steps_from_md(script_file)
            
            if not steps:
                print(f"⚠️ 未找到有效步骤: {script_file}")
                continue
            
            # 重新生成音频
            await main.batch_tts(steps, base_name)
            print(f"✅ 音频生成完成: {base_name}")
            
        except Exception as e:
            print(f"❌ 处理失败 {base_name}: {e}")

def main():
    """主函数"""
    print("🔧 音频文件修复工具")
    print("=" * 50)
    
    # 1. 清理损坏的音频文件
    clean_corrupted_audio()
    
    # 2. 重新生成音频文件
    try:
        asyncio.run(regenerate_audio())
        print("\n🎉 音频修复完成！")
    except Exception as e:
        print(f"\n❌ 音频重新生成失败: {e}")

if __name__ == "__main__":
    main()
