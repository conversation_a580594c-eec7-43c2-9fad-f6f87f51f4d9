#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能路由导航系统
基于APP项目的路由和模板分析，智能处理页面导航和点击事件
"""

import os
import re
import json
from typing import Dict, List, Optional, Tuple
from pathlib import Path

class SmartNavigator:
    """智能导航器"""
    
    def __init__(self):
        self.app_path = Path("APP")
        self.routes_map = {}
        self.templates_map = {}
        self.navigation_map = {}
        self._load_project_structure()
    
    def _load_project_structure(self):
        """加载项目结构"""
        print("🔍 分析APP项目结构...")
        self._analyze_routes()
        self._analyze_templates()
        self._build_navigation_map()
        print(f"✅ 项目分析完成: {len(self.routes_map)} 个路由, {len(self.templates_map)} 个模板")
    
    def _analyze_routes(self):
        """分析路由文件"""
        routes_dir = self.app_path / "routes"
        if not routes_dir.exists():
            return
        
        for route_file in routes_dir.glob("*.py"):
            if route_file.name.startswith("__"):
                continue
            
            module_name = route_file.stem
            routes = self._parse_route_file(route_file)
            self.routes_map[module_name] = routes
    
    def _parse_route_file(self, route_file: Path) -> Dict:
        """解析单个路由文件"""
        routes = {}
        
        try:
            with open(route_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取Blueprint名称
            bp_match = re.search(r'(\w+)_bp\s*=\s*Blueprint\([\'"](\w+)[\'"]', content)
            if not bp_match:
                return routes
            
            bp_var_name = bp_match.group(1)
            bp_name = bp_match.group(2)
            
            # 提取路由定义
            route_pattern = rf'@{bp_var_name}_bp\.route\([\'"]([^\'"]+)[\'"](?:,\s*methods=\[([^\]]+)\])?\)\s*(?:@\w+\s*)*def\s+(\w+)\([^)]*\):'
            
            for match in re.finditer(route_pattern, content):
                path = match.group(1)
                methods = match.group(2)
                func_name = match.group(3)
                
                # 解析方法
                if methods:
                    methods = [m.strip().strip('\'"') for m in methods.split(',')]
                else:
                    methods = ['GET']
                
                # 构建完整URL
                full_path = f"/{bp_name}{path}" if path != "/" else f"/{bp_name}"
                
                routes[func_name] = {
                    'path': full_path,
                    'methods': methods,
                    'blueprint': bp_name,
                    'function': func_name
                }
        
        except Exception as e:
            print(f"⚠️ 解析路由文件失败 {route_file}: {e}")
        
        return routes
    
    def _analyze_templates(self):
        """分析模板文件"""
        templates_dir = self.app_path / "templates"
        if not templates_dir.exists():
            return
        
        for template_file in templates_dir.rglob("*.html"):
            relative_path = template_file.relative_to(templates_dir)
            template_info = self._parse_template_file(template_file)
            self.templates_map[str(relative_path)] = template_info
    
    def _parse_template_file(self, template_file: Path) -> Dict:
        """解析模板文件"""
        template_info = {
            'buttons': [],
            'links': [],
            'forms': [],
            'navigation': []
        }
        
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取按钮
            button_patterns = [
                r'<button[^>]*class="[^"]*btn[^"]*"[^>]*>([^<]+)</button>',
                r'<a[^>]*class="[^"]*btn[^"]*"[^>]*>([^<]+)</a>',
                r'<input[^>]*type="submit"[^>]*value="([^"]+)"[^>]*>',
                r'<button[^>]*>([^<]+)</button>'
            ]
            
            for pattern in button_patterns:
                for match in re.finditer(pattern, content, re.IGNORECASE):
                    button_text = re.sub(r'<[^>]+>', '', match.group(1)).strip()
                    if button_text:
                        template_info['buttons'].append(button_text)
            
            # 提取链接
            link_pattern = r'<a[^>]*href="([^"]+)"[^>]*>([^<]+)</a>'
            for match in re.finditer(link_pattern, content, re.IGNORECASE):
                href = match.group(1)
                text = re.sub(r'<[^>]+>', '', match.group(2)).strip()
                if text and not href.startswith('#'):
                    template_info['links'].append({
                        'href': href,
                        'text': text
                    })
            
            # 提取url_for调用
            url_for_pattern = r'url_for\([\'"]([^\'"]+)[\'"](?:,\s*([^)]+))?\)'
            for match in re.finditer(url_for_pattern, content):
                endpoint = match.group(1)
                params = match.group(2) if match.group(2) else ''
                template_info['navigation'].append({
                    'endpoint': endpoint,
                    'params': params
                })
        
        except Exception as e:
            print(f"⚠️ 解析模板文件失败 {template_file}: {e}")
        
        return template_info
    
    def _build_navigation_map(self):
        """构建导航映射"""
        # 供应商模块的导航映射
        self.navigation_map = {
            '供应商分类': {
                'endpoint': 'supplier_category.index',
                'url': '/supplier_category/',
                'buttons': ['添加分类', '编辑', '删除'],
                'template': 'supplier/category_index.html'
            },
            '添加分类': {
                'endpoint': 'supplier_category.create',
                'url': '/supplier_category/create',
                'buttons': ['提交', '取消'],
                'template': 'supplier/category_form.html'
            },
            '供应商列表': {
                'endpoint': 'supplier.index',
                'url': '/supplier/',
                'buttons': ['添加供应商', '查看', '编辑', '删除'],
                'template': 'supplier/index.html'
            },
            '添加供应商': {
                'endpoint': 'supplier.create',
                'url': '/supplier/create',
                'buttons': ['提交', '取消'],
                'template': 'supplier/form.html'
            },
            '产品管理': {
                'endpoint': 'supplier_product.index',
                'url': '/supplier_product/',
                'buttons': ['添加产品', '批次管理', '审核通过', '拒绝', '上架', '下架', '查看详情'],
                'template': 'supplier/product_index.html'
            },
            '添加产品': {
                'endpoint': 'supplier_product.create',
                'url': '/supplier_product/create',
                'buttons': ['提交', '取消'],
                'template': 'supplier/product_form.html'
            },
            '批次管理': {
                'endpoint': 'supplier_product.index',
                'url': '/supplier_product/',
                'buttons': ['创建批次', '审核通过', '批量审核通过', '批量上架'],
                'template': 'supplier/product_index.html'
            }
        }
    
    def get_navigation_path(self, target_text: str) -> Optional[Dict]:
        """获取导航路径"""
        # 直接匹配
        if target_text in self.navigation_map:
            return self.navigation_map[target_text]
        
        # 模糊匹配
        for key, value in self.navigation_map.items():
            if target_text in key or key in target_text:
                return value
            
            # 检查按钮列表
            if target_text in value.get('buttons', []):
                return value
        
        return None
    
    def get_smart_selector(self, target_text: str, current_url: str = None) -> Optional[str]:
        """获取智能选择器"""
        # 获取导航信息
        nav_info = self.get_navigation_path(target_text)
        
        if not nav_info:
            return None
        
        # 生成候选选择器
        selectors = []
        
        # 基于文本的选择器
        selectors.extend([
            f'text="{target_text}"',
            f'text={target_text}',
            f'a:has-text("{target_text}")',
            f'button:has-text("{target_text}")',
        ])
        
        # 基于href的选择器（如果是链接）
        if 'url' in nav_info:
            url = nav_info['url']
            selectors.extend([
                f'a[href="{url}"]',
                f'a[href*="{url}"]',
                f'[onclick*="{url}"]'
            ])
        
        # 基于class的选择器
        if '添加' in target_text:
            selectors.extend([
                '.btn-primary:has-text("添加")',
                '[class*="add"]:has-text("添加")',
                '[class*="create"]:has-text("添加")'
            ])
        elif '编辑' in target_text:
            selectors.extend([
                '.btn-primary:has-text("编辑")',
                '[class*="edit"]:has-text("编辑")',
                '.fa-edit'
            ])
        elif '删除' in target_text:
            selectors.extend([
                '.btn-danger:has-text("删除")',
                '[class*="delete"]:has-text("删除")',
                '.fa-trash'
            ])
        elif '提交' in target_text:
            selectors.extend([
                'input[type="submit"]',
                'button[type="submit"]',
                '.btn-primary:has-text("提交")'
            ])
        
        return selectors[0] if selectors else None
    
    def get_navigation_sequence(self, target_page: str) -> List[Dict]:
        """获取导航序列"""
        sequences = {
            '供应商分类': [
                {'text': '供应商管理', 'type': 'menu'},
                {'text': '供应商分类', 'type': 'submenu'}
            ],
            '添加分类': [
                {'text': '供应商管理', 'type': 'menu'},
                {'text': '供应商分类', 'type': 'submenu'},
                {'text': '添加分类', 'type': 'button'}
            ],
            '供应商列表': [
                {'text': '供应商管理', 'type': 'menu'},
                {'text': '供应商列表', 'type': 'submenu'}
            ],
            '产品管理': [
                {'text': '供应商管理', 'type': 'menu'},
                {'text': '产品管理', 'type': 'submenu'}
            ]
        }
        
        return sequences.get(target_page, [])

# 全局导航器实例
navigator = SmartNavigator()

def get_smart_navigation_info(target_text: str) -> Dict:
    """获取智能导航信息"""
    return {
        'navigation_path': navigator.get_navigation_path(target_text),
        'smart_selector': navigator.get_smart_selector(target_text),
        'navigation_sequence': navigator.get_navigation_sequence(target_text)
    }

# 测试函数
def test_smart_navigator():
    """测试智能导航器"""
    print("🧪 测试智能导航器...")
    
    test_cases = [
        '供应商分类',
        '添加分类',
        '供应商列表',
        '添加供应商',
        '产品管理',
        '添加产品'
    ]
    
    for case in test_cases:
        info = get_smart_navigation_info(case)
        print(f"\n📍 {case}:")
        print(f"  导航路径: {info['navigation_path']}")
        print(f"  智能选择器: {info['smart_selector']}")
        print(f"  导航序列: {info['navigation_sequence']}")

if __name__ == "__main__":
    test_smart_navigator()
