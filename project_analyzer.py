#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目结构分析器
自动分析Flask项目结构，生成完整的配置文件
"""

import os
import re
import json
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Set
from datetime import datetime

class ProjectAnalyzer:
    """项目结构分析器"""
    
    def __init__(self, project_path: str = "APP"):
        self.project_path = Path(project_path)
        self.analysis_result = {
            'metadata': {
                'analyzed_at': datetime.now().isoformat(),
                'project_path': str(self.project_path),
                'version': '1.0'
            },
            'blueprints': {},
            'routes': {},
            'templates': {},
            'navigation': {},
            'selectors': {},
            'business_logic': {}
        }
    
    def analyze_project(self) -> Dict:
        """分析整个项目结构"""
        print("🔍 开始分析项目结构...")
        
        # 分析路由
        self._analyze_routes()
        
        # 分析模板
        self._analyze_templates()
        
        # 分析导航结构
        self._analyze_navigation()
        
        # 生成选择器映射
        self._generate_selector_mapping()
        
        # 分析业务逻辑
        self._analyze_business_logic()
        
        print(f"✅ 项目分析完成")
        print(f"   - Blueprints: {len(self.analysis_result['blueprints'])}")
        print(f"   - Routes: {len(self.analysis_result['routes'])}")
        print(f"   - Templates: {len(self.analysis_result['templates'])}")
        print(f"   - Navigation: {len(self.analysis_result['navigation'])}")
        
        return self.analysis_result
    
    def _analyze_routes(self):
        """分析路由文件"""
        routes_dir = self.project_path / "routes"
        if not routes_dir.exists():
            return
        
        print("📁 分析路由文件...")
        
        for route_file in routes_dir.glob("*.py"):
            if route_file.name.startswith("__"):
                continue
            
            module_name = route_file.stem
            blueprint_info = self._parse_route_file(route_file)
            
            if blueprint_info:
                self.analysis_result['blueprints'][module_name] = blueprint_info
                
                # 添加到路由映射
                for route_name, route_info in blueprint_info.get('routes', {}).items():
                    full_route_key = f"{module_name}.{route_name}"
                    self.analysis_result['routes'][full_route_key] = route_info
    
    def _parse_route_file(self, route_file: Path) -> Optional[Dict]:
        """解析单个路由文件"""
        try:
            with open(route_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取Blueprint信息
            bp_match = re.search(r'(\w+)_bp\s*=\s*Blueprint\([\'"](\w+)[\'"]', content)
            if not bp_match:
                return None
            
            bp_var_name = bp_match.group(1)
            bp_name = bp_match.group(2)
            
            blueprint_info = {
                'name': bp_name,
                'variable': bp_var_name,
                'file': str(route_file),
                'routes': {},
                'imports': [],
                'models': []
            }
            
            # 提取导入的模型
            model_imports = re.findall(r'from\s+app\.models\s+import\s+([^\\n]+)', content)
            for import_line in model_imports:
                models = [m.strip() for m in import_line.split(',')]
                blueprint_info['models'].extend(models)
            
            # 提取路由定义
            route_pattern = rf'@{bp_var_name}_bp\.route\([\'"]([^\'"]+)[\'"](?:,\s*methods=\[([^\]]+)\])?\)\s*(?:@\w+\s*)*def\s+(\w+)\([^)]*\):'
            
            for match in re.finditer(route_pattern, content):
                path = match.group(1)
                methods = match.group(2)
                func_name = match.group(3)
                
                # 解析方法
                if methods:
                    methods = [m.strip().strip('\'"') for m in methods.split(',')]
                else:
                    methods = ['GET']
                
                # 构建完整URL
                full_path = f"/{bp_name}{path}" if path != "/" else f"/{bp_name}"
                
                # 提取函数内容以分析功能
                func_content = self._extract_function_content(content, func_name)
                
                route_info = {
                    'path': full_path,
                    'methods': methods,
                    'blueprint': bp_name,
                    'function': func_name,
                    'template': self._extract_template_name(func_content),
                    'redirect_to': self._extract_redirect_targets(func_content),
                    'form_class': self._extract_form_class(func_content),
                    'description': self._extract_function_description(func_content)
                }
                
                blueprint_info['routes'][func_name] = route_info
            
            return blueprint_info
            
        except Exception as e:
            print(f"⚠️ 解析路由文件失败 {route_file}: {e}")
            return None
    
    def _extract_function_content(self, content: str, func_name: str) -> str:
        """提取函数内容"""
        pattern = rf'def\s+{func_name}\([^)]*\):(.*?)(?=\ndef\s+\w+|$)'
        match = re.search(pattern, content, re.DOTALL)
        return match.group(1) if match else ""
    
    def _extract_template_name(self, func_content: str) -> Optional[str]:
        """提取模板名称"""
        template_match = re.search(r'render_template\([\'"]([^\'"]+)[\'"]', func_content)
        return template_match.group(1) if template_match else None
    
    def _extract_redirect_targets(self, func_content: str) -> List[str]:
        """提取重定向目标"""
        redirect_matches = re.findall(r'redirect\(url_for\([\'"]([^\'"]+)[\'"]', func_content)
        return redirect_matches
    
    def _extract_form_class(self, func_content: str) -> Optional[str]:
        """提取表单类"""
        form_match = re.search(r'(\w+Form)\(\)', func_content)
        return form_match.group(1) if form_match else None
    
    def _extract_function_description(self, func_content: str) -> str:
        """提取函数描述"""
        # 提取文档字符串
        docstring_match = re.search(r'"""([^"]+)"""', func_content)
        if docstring_match:
            return docstring_match.group(1).strip()
        
        # 提取注释
        comment_match = re.search(r'#\s*(.+)', func_content)
        if comment_match:
            return comment_match.group(1).strip()
        
        return ""
    
    def _analyze_templates(self):
        """分析模板文件"""
        templates_dir = self.project_path / "templates"
        if not templates_dir.exists():
            return
        
        print("📄 分析模板文件...")
        
        for template_file in templates_dir.rglob("*.html"):
            relative_path = template_file.relative_to(templates_dir)
            template_info = self._parse_template_file(template_file)
            self.analysis_result['templates'][str(relative_path)] = template_info
    
    def _parse_template_file(self, template_file: Path) -> Dict:
        """解析模板文件"""
        template_info = {
            'file': str(template_file),
            'buttons': [],
            'links': [],
            'forms': [],
            'navigation_elements': [],
            'css_classes': set(),
            'javascript_functions': [],
            'url_for_calls': []
        }
        
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取按钮
            button_patterns = [
                r'<button[^>]*class="([^"]*)"[^>]*>([^<]+)</button>',
                r'<a[^>]*class="([^"]*btn[^"]*)"[^>]*>([^<]+)</a>',
                r'<input[^>]*type="submit"[^>]*value="([^"]+)"[^>]*class="([^"]*)"[^>]*>',
            ]
            
            for pattern in button_patterns:
                for match in re.finditer(pattern, content, re.IGNORECASE):
                    if len(match.groups()) == 2:
                        css_class, text = match.groups()
                        template_info['buttons'].append({
                            'text': self._clean_text(text),
                            'css_class': css_class,
                            'type': 'button'
                        })
            
            # 提取链接
            link_pattern = r'<a[^>]*href="([^"]+)"[^>]*class="([^"]*)"[^>]*>([^<]+)</a>'
            for match in re.finditer(link_pattern, content, re.IGNORECASE):
                href, css_class, text = match.groups()
                if not href.startswith('#'):
                    template_info['links'].append({
                        'href': href,
                        'text': self._clean_text(text),
                        'css_class': css_class
                    })
            
            # 提取url_for调用
            url_for_pattern = r'url_for\([\'"]([^\'"]+)[\'"](?:,\s*([^)]+))?\)'
            for match in re.finditer(url_for_pattern, content):
                endpoint = match.group(1)
                params = match.group(2) if match.group(2) else ''
                template_info['url_for_calls'].append({
                    'endpoint': endpoint,
                    'params': params
                })
            
            # 提取CSS类
            css_class_pattern = r'class="([^"]+)"'
            for match in re.finditer(css_class_pattern, content):
                classes = match.group(1).split()
                template_info['css_classes'].update(classes)
            
            # 转换set为list以便JSON序列化
            template_info['css_classes'] = list(template_info['css_classes'])
            
        except Exception as e:
            print(f"⚠️ 解析模板文件失败 {template_file}: {e}")
        
        return template_info
    
    def _clean_text(self, text: str) -> str:
        """清理文本内容"""
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        # 移除多余空白
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    def _analyze_navigation(self):
        """分析导航结构"""
        print("🧭 分析导航结构...")
        
        # 基于路由和模板分析构建导航映射
        navigation = {}
        
        # 供应商模块导航
        supplier_nav = {
            'module': 'supplier',
            'name': '供应商管理',
            'icon': 'fas fa-truck',
            'children': {
                '供应商分类': {
                    'endpoint': 'supplier_category.index',
                    'url': '/supplier_category/',
                    'template': 'supplier/category_index.html',
                    'actions': ['添加分类', '编辑', '删除'],
                    'selectors': {
                        'menu_link': 'a[href*="/supplier_category/"]:has-text("供应商分类")',
                        'add_button': '.btn-primary:has-text("添加分类")',
                        'edit_button': '.btn-primary:has-text("编辑")',
                        'delete_button': '.btn-danger:has-text("删除")'
                    }
                },
                '供应商列表': {
                    'endpoint': 'supplier.index',
                    'url': '/supplier/',
                    'template': 'supplier/index.html',
                    'actions': ['添加供应商', '查看', '编辑', '删除'],
                    'selectors': {
                        'menu_link': 'a[href*="/supplier/"]:has-text("供应商列表")',
                        'add_button': '.btn-primary:has-text("添加供应商")',
                        'view_button': '.btn-info:has-text("查看")',
                        'edit_button': '.btn-primary:has-text("编辑")',
                        'delete_button': '.btn-danger:has-text("删除")'
                    }
                },
                '产品管理': {
                    'endpoint': 'supplier_product.index',
                    'url': '/supplier_product/',
                    'template': 'supplier/product_index.html',
                    'actions': ['添加产品', '批次管理', '审核通过', '拒绝', '上架', '下架', '查看详情'],
                    'selectors': {
                        'menu_link': 'a[href*="/supplier_product/"]:has-text("产品管理")',
                        'add_button': '.btn-primary:has-text("添加产品")',
                        'batch_button': '.btn-info:has-text("批次管理")',
                        'approve_button': '.btn-success:has-text("审核通过")',
                        'reject_button': '.btn-danger:has-text("拒绝")',
                        'shelf_button': '.btn-success:has-text("上架")',
                        'unshelf_button': '.btn-warning:has-text("下架")',
                        'detail_button': '.btn-info:has-text("查看详情")'
                    }
                }
            }
        }
        
        navigation['supplier'] = supplier_nav
        self.analysis_result['navigation'] = navigation
    
    def _generate_selector_mapping(self):
        """生成选择器映射"""
        print("🎯 生成选择器映射...")
        
        selectors = {
            # 通用选择器
            'common': {
                'submit_button': ['input[type="submit"]', 'button[type="submit"]', '.btn-primary:has-text("提交")'],
                'cancel_button': ['.btn-secondary:has-text("取消")', 'a:has-text("取消")'],
                'close_button': ['.close', 'button:has-text("关闭")', '[aria-label="关闭"]'],
                'confirm_button': ['.btn-primary:has-text("确认")', 'button:has-text("确认")'],
                'back_button': ['.btn-secondary:has-text("返回")', 'a:has-text("返回")']
            },
            
            # 操作类型选择器
            'actions': {
                'add': ['.btn-primary:has-text("添加")', 'a:has-text("添加")', '[class*="add"]:has-text("添加")'],
                'edit': ['.btn-primary:has-text("编辑")', 'a:has-text("编辑")', '.fa-edit', '[class*="edit"]'],
                'delete': ['.btn-danger:has-text("删除")', 'button:has-text("删除")', '.fa-trash', '[class*="delete"]'],
                'view': ['.btn-info:has-text("查看")', 'a:has-text("查看")', '.fa-eye', '[class*="view"]'],
                'save': ['.btn-success:has-text("保存")', 'button:has-text("保存")', '[class*="save"]'],
                'search': ['.btn-primary:has-text("搜索")', 'button:has-text("搜索")', '[class*="search"]']
            },
            
            # 模块特定选择器
            'modules': {
                'supplier': {
                    'category_link': ['a[href*="/supplier_category/"]:has-text("供应商分类")', '.nav-link:has-text("供应商分类")'],
                    'list_link': ['a[href*="/supplier/"]:has-text("供应商列表")', '.nav-link:has-text("供应商列表")'],
                    'product_link': ['a[href*="/supplier_product/"]:has-text("产品管理")', '.nav-link:has-text("产品管理")']
                }
            }
        }
        
        self.analysis_result['selectors'] = selectors
    
    def _analyze_business_logic(self):
        """分析业务逻辑"""
        print("💼 分析业务逻辑...")
        
        business_logic = {
            'supplier_workflow': {
                'description': '供应商管理工作流程',
                'steps': [
                    {
                        'step': 1,
                        'name': '供应商分类管理',
                        'description': '创建和管理供应商分类',
                        'pages': ['supplier_category.index', 'supplier_category.create'],
                        'prerequisites': [],
                        'next_steps': ['supplier.create']
                    },
                    {
                        'step': 2,
                        'name': '供应商信息管理',
                        'description': '添加和管理供应商基本信息',
                        'pages': ['supplier.index', 'supplier.create'],
                        'prerequisites': ['supplier_category.index'],
                        'next_steps': ['supplier_product.create']
                    },
                    {
                        'step': 3,
                        'name': '供应商产品管理',
                        'description': '管理供应商提供的产品',
                        'pages': ['supplier_product.index', 'supplier_product.create'],
                        'prerequisites': ['supplier.index'],
                        'next_steps': ['supplier_product.approve']
                    }
                ]
            }
        }
        
        self.analysis_result['business_logic'] = business_logic
    
    def save_config(self, output_file: str = "project_config.json"):
        """保存配置文件"""
        print(f"💾 保存配置文件: {output_file}")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_result, f, ensure_ascii=False, indent=2)
        
        # 同时保存YAML格式
        yaml_file = output_file.replace('.json', '.yaml')
        with open(yaml_file, 'w', encoding='utf-8') as f:
            yaml.dump(self.analysis_result, f, default_flow_style=False, allow_unicode=True)
        
        print(f"✅ 配置文件已保存:")
        print(f"   - JSON格式: {output_file}")
        print(f"   - YAML格式: {yaml_file}")

def main():
    """主函数"""
    analyzer = ProjectAnalyzer("APP")
    result = analyzer.analyze_project()
    analyzer.save_config("project_config.json")
    
    print("\n🎉 项目分析完成！")
    print("📋 生成的配置文件包含:")
    print("   - 路由映射和导航结构")
    print("   - 模板分析和选择器映射")
    print("   - 业务逻辑工作流程")
    print("   - 智能修复策略配置")

if __name__ == "__main__":
    main()
