import os
import re
import json
import glob
import asyncio
import sys
import time
from pydub import AudioSegment
import edge_tts

VIDEO_SCRIPTS_DIR = "video-scripts"
OUTPUT_DIR = "output"
ERROR_LOG = os.path.join(OUTPUT_DIR, "error.log")

def log(msg):
    print(msg)
    with open(ERROR_LOG, "a", encoding="utf-8") as f:
        f.write(msg + "\n")

def check_dependencies():
    try:
        import edge_tts
    except ImportError:
        print("缺少 edge-tts，请先运行: pip install edge-tts")
        sys.exit(1)
    try:
        import playwright
    except ImportError:
        print("缺少 playwright，请先运行: pip install playwright && playwright install")
        sys.exit(1)

def unique_name(base, ext):
    ts = time.strftime("%Y%m%d_%H%M%S")
    return f"{base}_{ts}.{ext}"

def guess_action_and_selector(op_line):
    """改进的操作解析函数，修复选择器提取问题"""
    op_line = op_line.strip()
    
    if op_line.startswith("点击"):
        # 优化正则表达式，更准确地提取按钮文本
        # 先尝试提取引号内的完整文本
        m = re.search(r'点击["\"]([^"\"]+)["\"]', op_line)
        if m:
            btn_text = m.group(1).strip()
            return "click", f"text={btn_text}"

        # 处理特殊格式：点击顶部导航栏"供应链"
        m = re.search(r'点击.*?["\"]([^"\"]+)["\"]', op_line)
        if m:
            btn_text = m.group(1).strip()
            return "click", f"text={btn_text}"

        # 如果没有引号，尝试提取关键词
        m = re.search(r'点击(.+?)(?:按钮|进行|，|。|$)', op_line)
        if m:
            btn_text = m.group(1).strip()
            # 清理多余的描述文字
            btn_text = re.sub(r'(顶部导航栏|按钮|菜单)', '', btn_text).strip()
            return "click", f"text={btn_text}"

        return "click", ""  # fallback
        
    elif op_line.startswith("输入"):
        # 如: 输入供应商名称"张三"
        m = re.search(r'输入(.+?)["\"]([^"\"]+)["\"]', op_line)
        if m:
            field, value = m.group(1).strip(), m.group(2).strip()
            return "fill", f"input[placeholder*='{field}']", value
        else:
            return "fill", "input", ""
            
    elif op_line.startswith("选择"):
        m = re.search(r'选择["\"]([^"\"]+)["\"]', op_line)
        if m:
            return "click", f"text={m.group(1)}"
        else:
            return "click", ""
            
    elif op_line.startswith("在") and "输入" in op_line:
        # 如: 在关键词搜索框输入"蔬菜"
        m = re.search(r'在(.+?)输入["\"]([^"\"]+)["\"]', op_line)
        if m:
            field, value = m.group(1).strip(), m.group(2).strip()
            return "fill", f"input[placeholder*='{field}']", value
        else:
            return "fill", "input", ""
            
    elif op_line.startswith("访问"):
        m = re.search(r'访问\s*([a-zA-Z0-9:\./_-]+)', op_line)
        if m:
            return "goto", "", m.group(1)
        else:
            return "goto", "", ""
            
    elif op_line.startswith("展示") or op_line.startswith("指向"):
        return "wait", "", "2000"  # 等待2秒用于展示
    else:
        return "wait", "", "1000"  # 默认等待1秒

def extract_steps_from_md(md_path):
    """改进的步骤提取函数，修复重复解说词问题"""
    with open(md_path, encoding="utf-8") as f:
        lines = f.readlines()
    
    steps = []
    i = 0
    
    while i < len(lines):
        line = lines[i].strip()
        
        if line.startswith("**操作：**"):
            # 收集所有操作行
            op_lines = []
            i += 1
            while i < len(lines) and (lines[i].strip().startswith("-") or lines[i].strip() == ""):
                if lines[i].strip().startswith("-"):
                    op_lines.append(lines[i].strip()[1:].strip())
                i += 1
            
            # 查找对应的解说词
            narration = ""
            while i < len(lines):
                narr_line = lines[i].strip()
                if narr_line.startswith("**解说词：**"):
                    narration = narr_line.replace("**解说词：**", "").strip().strip('"')
                    # 如果解说词在同一行为空，检查下一行
                    if not narration and i+1 < len(lines):
                        next_line = lines[i+1].strip().strip('"')
                        if next_line and not next_line.startswith("**"):
                            narration = next_line
                            i += 1
                    break
                i += 1
            
            # 为每个操作创建独立的步骤
            for idx, op in enumerate(op_lines):
                action, selector, *value = guess_action_and_selector(op)
                step = {
                    "narration": narration,
                    "action": action,
                    "selector": selector,
                    "operation": op  # 保留原始操作描述用于调试
                }
                if value and value[0]:
                    step["value"] = value[0]
                steps.append(step)
        else:
            i += 1
    
    return steps

async def tts(text, mp3_path):
    communicate = edge_tts.Communicate(text, "zh-CN-XiaoxiaoNeural")
    await communicate.save(mp3_path)

async def batch_tts(steps, base_name):
    mp3_paths = []
    for idx, step in enumerate(steps, 1):
        mp3_path = os.path.join(OUTPUT_DIR, f"{base_name}_step{idx}.mp3")
        if not os.path.exists(mp3_path):
            await tts(step["narration"], mp3_path)
        mp3_paths.append(mp3_path)
    return mp3_paths

def generate_actions_json(steps, base_name):
    actions = []
    for idx, step in enumerate(steps, 1):
        action_obj = {
            "step": idx,
            "narration": step["narration"],
            "audio": f"{base_name}_step{idx}.mp3",
            "action": step["action"],
            "selector": step["selector"],
            "operation": step.get("operation", "")  # 添加原始操作描述
        }
        if step.get("value"):
            action_obj["value"] = step["value"]
        actions.append(action_obj)
    
    json_path = os.path.join(OUTPUT_DIR, f"{base_name}_actions.json")
    with open(json_path, "w", encoding="utf-8") as f:
        json.dump(actions, f, ensure_ascii=False, indent=2)
    return json_path

async def process_one_md(md_path):
    base_name = os.path.splitext(os.path.basename(md_path))[0]
    steps = extract_steps_from_md(md_path)
    
    # 保存有效步骤到 steps.json
    steps_json_path = os.path.join(OUTPUT_DIR, f"{base_name}_steps.json")
    with open(steps_json_path, "w", encoding="utf-8") as f:
        json.dump(steps, f, ensure_ascii=False, indent=2)
    
    if not steps:
        log(f"未在 {md_path} 中找到有效步骤，跳过。")
        return
    
    log(f"正在处理: {base_name} (共{len(steps)}个步骤)")
    
    try:
        await batch_tts(steps, base_name)
        actions_json = generate_actions_json(steps, base_name)
        mp4_path = os.path.join(OUTPUT_DIR, unique_name(base_name, 'mp4'))
        
        # 显示解析结果用于调试
        print(f"\n=== {base_name} 解析结果 ===")
        for idx, step in enumerate(steps, 1):
            print(f"步骤{idx}: {step['operation']} -> {step['action']}({step['selector']})")
        
        ret = os.system(f"python run_playwright.py {actions_json} {mp4_path}")
        if ret != 0:
            log(f"录制 {base_name} 失败，返回码: {ret}")
        else:
            log(f"录制完成: {mp4_path}")
    except Exception as e:
        log(f"处理 {base_name} 失败: {e}")

def main():
    check_dependencies()
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    if os.path.exists(ERROR_LOG):
        os.remove(ERROR_LOG)
    
    md_files = glob.glob(os.path.join(VIDEO_SCRIPTS_DIR, "*.md"))
    if not md_files:
        print("未找到任何 markdown 脚本。")
        return
    
    print(f"共检测到 {len(md_files)} 个模块，开始自动化处理...")
    loop = asyncio.get_event_loop()
    tasks = [process_one_md(md) for md in md_files]
    loop.run_until_complete(asyncio.gather(*tasks))
    
    print("全部处理完成！如有错误请查看 output/error.log")
    
    # 自动打开 output 目录
    if sys.platform.startswith("win"):
        os.system(f"start {OUTPUT_DIR}")
    else:
        os.system(f"open {OUTPUT_DIR}")

if __name__ == "__main__":
    main()
