# 智慧食堂教学视频录制脚本生成规则

## 📋 项目概述

本规则用于指导AI编程工具自动生成 http://xiaoyuanst.com 智慧食堂平台的教学操作视频录制脚本。目标是全面、系统地挖掘网站的所有功能模块，生成标准化的操作步骤、时间戳和解说词，帮助用户深入理解整个智慧食堂管理系统。

## 🎯 生成目标

### 核心目标
- **全面性**：覆盖网站所有功能模块，不遗漏任何重要功能
- **系统性**：按照业务逻辑和用户使用流程组织内容
- **实用性**：生成可直接用于视频录制的标准化脚本
- **教学性**：帮助用户理解系统架构和业务流程

### 输出要求
- 生成带时间戳的Markdown格式脚本
- 包含详细的操作步骤和解说词
- 符合视频录制工具的解析要求
- 提供多个难度层次的教学内容

## 🏗️ 网站架构分析

### 主要功能模块
基于智慧食堂管理系统的特点，需要重点挖掘以下模块：

#### 1. 用户管理模块
- 用户注册、登录、权限管理
- 角色分配（管理员、食堂工作人员、学生等）
- 个人信息管理、密码修改

#### 2. 供应链管理模块
- **供应商管理**：供应商信息、分类、评级
- **采购管理**：采购订单、智能采购、采购审批
- **库存管理**：入库、出库、库存盘点、预警
- **仓库管理**：仓库信息、区域管理

#### 3. 食材管理模块
- **食材档案**：食材分类、基本信息、营养成分
- **食材溯源**：来源追踪、质量检测记录
- **食材标准**：质量标准、规格管理

#### 4. 菜谱管理模块
- **菜谱创建**：菜谱信息、制作工艺、营养分析
- **菜谱分类**：按类型、口味、营养等分类
- **成本核算**：食材成本、人工成本计算

#### 5. 餐饮服务模块
- **菜单管理**：每日菜单、特色菜品、价格管理
- **订餐服务**：在线订餐、预约用餐
- **营养配餐**：营养搭配、健康建议

#### 6. 财务管理模块
- **收支管理**：收入统计、支出记录
- **成本分析**：食材成本、运营成本
- **财务报表**：日报、月报、年报

#### 7. 数据分析模块
- **销售分析**：销量统计、趋势分析
- **用户行为分析**：用餐习惯、偏好分析
- **运营分析**：效率分析、优化建议

#### 8. 系统管理模块
- **基础设置**：系统参数、基础数据
- **权限管理**：角色权限、功能权限
- **日志管理**：操作日志、系统日志

## 📝 脚本生成规则

### 1. 脚本结构标准

#### 文件命名规则
```
{模块名称}-{功能名称}-demo.md
例如：supplier-management-demo.md
```

#### 标准脚本模板
```markdown
# {模块名称}全功能视频录制脚本

## 视频概述
**标题**: 智慧食堂 {模块名称}完整操作指南  
**时长**: 约{X}分钟  
**目标用户**: {目标用户群体}  
**难度等级**: {初级/中级/高级}

## 时间轴概览
- 00:00-01:00 开场介绍和模块概述
- 01:00-{X}:00 核心功能演示
- {X}:00-{Y}:00 高级功能和技巧
- {Y}:00-{Z}:00 总结和最佳实践

## 功能演示步骤

### 步骤一：{功能名称} [{开始时间}-{结束时间}]

#### 1.1 {子功能名称} [{开始时间}-{结束时间}]
**时间戳**: {开始时间}-{结束时间}  
**解说词**: "{详细的解说内容，说明操作目的和业务意义}"

**操作步骤**:
- [{具体时间}] {具体操作描述}
- [{具体时间}] {具体操作描述}
```

### 2. 操作步骤规范

#### 必须包含的登录流程
每个脚本开始必须包含：
```markdown
### 系统登录 [00:00-01:00]
**解说词**: "首先我们访问智慧食堂平台，使用游客账号进行演示..."

**操作步骤**:
- [00:05] 访问 http://xiaoyuanst.com
- [00:15] 点击"体验系统"按钮
- [00:25] 等待系统加载完成
```

#### 操作描述标准
- **点击操作**：`点击"{按钮文本}"`
- **输入操作**：`在{字段名称}输入"{内容}"`
- **选择操作**：`选择"{选项名称}"`
- **导航操作**：`点击导航栏"{菜单名称}"`
- **等待操作**：`等待{时间}秒加载完成`

### 3. 解说词编写规则

#### 解说词结构
1. **操作目的**：说明为什么要进行这个操作
2. **业务背景**：解释功能在实际业务中的作用
3. **操作指导**：详细说明如何操作
4. **注意事项**：提醒重要的注意点
5. **扩展说明**：相关功能或最佳实践

#### 解说词示例
```
"现在我们进入供应商管理模块。供应商管理是食材溯源的重要环节，
通过建立完善的供应商档案，我们可以确保食材来源的可靠性和质量的可控性。
点击左侧导航的'供应商管理'，可以看到包含供应商信息、产品管理、
合同管理等多个子功能。这种模块化的设计让管理更加高效便捷。"
```

### 4. 时间戳分配规则

#### 时间分配原则
- **简单操作**：5-10秒
- **表单填写**：15-30秒
- **页面加载**：3-5秒
- **功能演示**：30-60秒
- **复杂流程**：2-5分钟

#### 时间戳格式
- 使用 `[MM:SS]` 格式
- 精确到秒级
- 包含开始和结束时间

## 🔍 功能挖掘策略

### 1. 系统性探索方法

#### 导航结构分析
1. **主导航菜单**：逐一点击每个主菜单项
2. **子菜单展开**：展开所有子菜单和下拉选项
3. **面包屑导航**：跟踪页面层级关系
4. **快捷入口**：发现首页或仪表板的快捷功能

#### 页面元素分析
1. **功能按钮**：识别所有操作按钮
2. **表单字段**：分析输入字段和选项
3. **数据表格**：查看列表、筛选、排序功能
4. **弹窗对话框**：触发各种对话框和模态窗口

### 2. 业务流程挖掘

#### 完整业务链条
1. **数据录入**：从基础数据开始
2. **业务操作**：核心业务流程
3. **数据查询**：各种查询和报表
4. **系统维护**：管理和配置功能

#### 用户角色视角
1. **管理员视角**：系统配置、用户管理
2. **业务人员视角**：日常操作、数据录入
3. **查询用户视角**：数据查看、报表导出

### 3. 功能深度挖掘

#### 隐藏功能发现
1. **右键菜单**：尝试右键点击各种元素
2. **快捷键**：测试常见快捷键组合
3. **批量操作**：选择多项进行批量处理
4. **高级搜索**：使用各种搜索和筛选条件

#### 异常情况处理
1. **错误处理**：故意输入错误数据观察反应
2. **边界测试**：测试极限值和特殊字符
3. **权限测试**：不同权限下的功能差异

## 📊 脚本质量标准

### 1. 完整性检查
- [ ] 覆盖所有主要功能模块
- [ ] 包含完整的操作流程
- [ ] 涵盖不同用户角色的使用场景
- [ ] 包含异常处理和错误恢复

### 2. 准确性验证
- [ ] 操作步骤可以实际执行
- [ ] 选择器和元素定位准确
- [ ] 时间戳分配合理
- [ ] 解说词与操作匹配

### 3. 教学效果评估
- [ ] 逻辑清晰，易于理解
- [ ] 循序渐进，难度适中
- [ ] 实用性强，贴近实际使用
- [ ] 包含最佳实践和技巧分享

## 🎬 视频制作要求

### 技术规格
- **分辨率**：1920x1080 (Full HD)
- **帧率**：30fps
- **音频**：清晰的中文解说，192kbps AAC
- **字幕**：中文字幕同步显示

### 后期制作
- **操作高亮**：重要操作添加高亮效果
- **标注说明**：关键信息添加文字标注
- **章节分割**：按功能模块分割章节
- **片头片尾**：统一的品牌标识

## 🚀 实施建议

### 分阶段实施
1. **第一阶段**：核心功能模块（供应链、菜谱、财务）
2. **第二阶段**：管理功能模块（用户、权限、系统）
3. **第三阶段**：高级功能模块（分析、报表、API）

### 持续优化
1. **用户反馈**：收集用户使用反馈
2. **功能更新**：跟踪系统功能更新
3. **脚本维护**：定期更新和优化脚本
4. **质量提升**：不断改进录制质量

## 📚 具体模块脚本生成指南

### 供应商管理模块脚本
```markdown
# 供应商管理全功能演示脚本

## 核心功能点
1. 供应商分类管理（增删改查）
2. 供应商基本信息管理
3. 供应商产品管理
4. 供应商评级和审核
5. 合同管理
6. 供应商报表和分析

## 必须演示的操作
- 创建供应商分类
- 添加新供应商
- 批量导入供应商信息
- 供应商产品上架/下架
- 供应商评级操作
- 生成供应商报表
```

### 菜谱管理模块脚本
```markdown
# 菜谱管理全功能演示脚本

## 核心功能点
1. 菜谱分类管理
2. 菜谱创建和编辑
3. 食材配比管理
4. 营养成分分析
5. 成本核算
6. 菜谱审核流程

## 必须演示的操作
- 创建菜谱分类
- 新建完整菜谱
- 添加制作工艺
- 计算营养成分
- 成本分析
- 菜谱发布流程
```

### 库存管理模块脚本
```markdown
# 库存管理全功能演示脚本

## 核心功能点
1. 入库管理（采购入库、退货入库）
2. 出库管理（领用出库、调拨出库）
3. 库存盘点
4. 库存预警
5. 库存报表
6. 仓库管理

## 必须演示的操作
- 采购入库流程
- 食材出库流程
- 库存盘点操作
- 设置库存预警
- 生成库存报表
- 仓库区域管理
```

## 🎯 AI生成脚本的具体提示词模板

### 基础提示词
```
请为智慧食堂管理系统（http://xiaoyuanst.com）的{模块名称}生成一个完整的教学视频录制脚本。

要求：
1. 使用带时间戳的Markdown格式
2. 包含详细的操作步骤和解说词
3. 覆盖该模块的所有核心功能
4. 适合{目标用户}使用
5. 视频时长控制在{X}分钟左右

请按照以下结构生成：
- 视频概述和时间轴
- 系统登录流程
- 各功能点的详细演示
- 最佳实践和注意事项
- 总结和扩展建议
```

### 高级提示词
```
作为智慧食堂系统的专家，请深度分析{模块名称}的业务逻辑和用户需求，
生成一个专业级的教学视频脚本。

分析要点：
1. 该模块在整个食堂管理体系中的作用
2. 不同角色用户的使用场景
3. 常见的业务流程和操作路径
4. 可能遇到的问题和解决方案
5. 与其他模块的关联关系

输出要求：
- 业务逻辑清晰，操作流程完整
- 解说词专业且易懂
- 包含实际业务场景的案例
- 提供操作技巧和最佳实践
- 时间戳精确，操作可执行
```

## 🔧 脚本验证和优化流程

### 自动验证检查点
1. **格式验证**
   - [ ] Markdown语法正确
   - [ ] 时间戳格式统一
   - [ ] 操作步骤格式标准

2. **内容验证**
   - [ ] 包含必要的登录步骤
   - [ ] 操作步骤逻辑连贯
   - [ ] 解说词与操作匹配
   - [ ] 时间分配合理

3. **功能覆盖验证**
   - [ ] 核心功能全覆盖
   - [ ] 包含异常处理
   - [ ] 涵盖不同用户角色
   - [ ] 包含最佳实践

### 人工审核要点
1. **业务准确性**：操作流程符合实际业务逻辑
2. **教学效果**：内容易懂，循序渐进
3. **实用性**：贴近实际使用场景
4. **完整性**：功能覆盖全面，无遗漏

## 📈 持续改进机制

### 反馈收集
1. **用户反馈**：收集观看者的意见和建议
2. **使用数据**：分析视频观看数据和用户行为
3. **系统更新**：跟踪网站功能更新和变化
4. **行业趋势**：关注智慧食堂行业发展趋势

### 优化策略
1. **内容更新**：定期更新过时的操作步骤
2. **质量提升**：改进解说词和操作流程
3. **功能扩展**：添加新发现的功能点
4. **用户体验**：优化视频结构和节奏

---

## 🎉 总结

本规则文档为AI编程工具提供了全面、系统的脚本生成指南，确保生成的教学视频脚本具有：

- **专业性**：符合行业标准和最佳实践
- **完整性**：覆盖系统所有重要功能
- **实用性**：贴近实际使用场景
- **教学性**：帮助用户深入理解系统

通过遵循这些规则，AI工具可以生成高质量的视频录制脚本，为用户提供优秀的学习体验，帮助他们更好地理解和使用智慧食堂管理系统。
