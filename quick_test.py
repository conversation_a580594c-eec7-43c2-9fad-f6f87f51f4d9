#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
sys.path.append('.')

from main_fixed import guess_action_and_selector

def test_parsing():
    test_cases = [
        '点击顶部导航栏"供应链"',
        '点击"体验系统"进行游客登录',
        '点击"入库管理"进入入库管理页面',
        '点击"供应商"',
        '访问 xiaoyuanst.com'
    ]
    
    print("🧪 测试修复后的解析效果")
    print("=" * 40)
    
    for case in test_cases:
        action, selector, *value = guess_action_and_selector(case)
        print(f"操作: {case}")
        print(f"  -> {action}({selector})")
        if value and value[0]:
            print(f"  -> 值: {value[0]}")
        print()

if __name__ == "__main__":
    test_parsing()
