[{"step": 1, "narration": "首先我们登录智慧食堂平台。我使用游客账号登录，这样您也可以跟着一起操作体验。登录成功后，我们可以看到系统的主界面，显示当前登录用户为guest_demo。", "audio": "test-single-module_step1.mp3", "action": "goto", "selector": "", "operation": "访问 xiaoyuanst.com", "value": "xiaoyuanst.com"}, {"step": 2, "narration": "首先我们登录智慧食堂平台。我使用游客账号登录，这样您也可以跟着一起操作体验。登录成功后，我们可以看到系统的主界面，显示当前登录用户为guest_demo。", "audio": "test-single-module_step2.mp3", "action": "click", "selector": "text=体验系统", "operation": "点击\"体验系统\"进行游客登录"}, {"step": 3, "narration": "现在我们进入入库管理模块。点击顶部的'供应链'菜单，然后选择'入库管理'。入库管理是供应链管理的核心环节，它连接了采购订单和库存管理，确保食材质量和数量的准确记录。", "audio": "test-single-module_step3.mp3", "action": "click", "selector": "text=供应链", "operation": "点击顶部导航栏\"供应链\""}, {"step": 4, "narration": "现在我们进入入库管理模块。点击顶部的'供应链'菜单，然后选择'入库管理'。入库管理是供应链管理的核心环节，它连接了采购订单和库存管理，确保食材质量和数量的准确记录。", "audio": "test-single-module_step4.mp3", "action": "click", "selector": "text=入库管理", "operation": "点击\"入库管理\"进入入库管理页面"}]