#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能脚本修复器
基于APP项目结构分析，智能修复录制脚本中的选择器和导航问题
"""

import json
import asyncio
from typing import Dict, List
from playwright.async_api import async_playwright
from smart_navigator import get_smart_navigation_info
from guide_detector import smart_skip_guides

class IntelligentScriptFixer:
    """智能脚本修复器"""
    
    def __init__(self):
        self.page = None
        self.browser = None
        self.context = None
        self.playwright = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(headless=False)
        self.context = await self.browser.new_context()
        self.page = await self.context.new_page()
        self.page.set_default_timeout(30000)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        try:
            if self.page and not self.page.is_closed():
                await self.page.close()
        except:
            pass
        
        try:
            if self.context:
                await self.context.close()
        except:
            pass
        
        try:
            if self.browser:
                await self.browser.close()
        except:
            pass
        
        try:
            if self.playwright:
                await self.playwright.stop()
        except:
            pass
    
    async def fix_script(self, actions_file: str) -> str:
        """修复脚本文件"""
        print(f"🔧 开始智能修复脚本: {actions_file}")
        
        # 加载原始脚本
        with open(actions_file, 'r', encoding='utf-8') as f:
            actions = json.load(f)
        
        # 初始化页面
        await self._initialize_page()
        
        # 修复每个操作
        fixed_actions = []
        for i, action in enumerate(actions, 1):
            print(f"🔍 修复步骤 {i}/{len(actions)}: {action.get('action')} - {action.get('narration', '')[:50]}...")
            
            fixed_action = await self._fix_single_action(action, i)
            fixed_actions.append(fixed_action)
        
        # 保存修复后的脚本
        fixed_file = actions_file.replace('.json', '_intelligent_fixed.json')
        with open(fixed_file, 'w', encoding='utf-8') as f:
            json.dump(fixed_actions, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 智能修复完成，保存到: {fixed_file}")
        return fixed_file
    
    async def _initialize_page(self):
        """初始化页面"""
        print("🌐 初始化页面...")
        
        # 访问网站
        await self.page.goto("http://xiaoyuanst.com", wait_until='networkidle')
        
        # 点击体验系统
        await self.page.click("text=体验系统")
        await self.page.wait_for_timeout(3000)
        
        # 智能跳过引导
        guide_result = await smart_skip_guides(self.page)
        print(f"✅ 引导跳过完成: {guide_result['final_state']}")
        
        # 等待页面稳定
        await self.page.wait_for_timeout(2000)
    
    async def _fix_single_action(self, action: Dict, step_num: int) -> Dict:
        """修复单个操作"""
        fixed_action = action.copy()
        
        action_type = action.get('action')
        
        if action_type == 'goto':
            # goto操作通常不需要修复
            pass
        
        elif action_type == 'click':
            fixed_selector = await self._fix_click_selector(action)
            if fixed_selector:
                fixed_action['selector'] = fixed_selector
                fixed_action['original_selector'] = action.get('selector')
        
        elif action_type == 'click_skip_guide':
            # 跳过引导操作已经在初始化时处理
            pass
        
        elif action_type == 'wait':
            # wait操作通常不需要修复
            pass
        
        elif action_type == 'fill':
            fixed_selector = await self._fix_fill_selector(action)
            if fixed_selector:
                fixed_action['selector'] = fixed_selector
                fixed_action['original_selector'] = action.get('selector')
        
        return fixed_action
    
    async def _fix_click_selector(self, action: Dict) -> str:
        """修复点击选择器"""
        original_selector = action.get('selector', '')
        if not original_selector:
            return original_selector
        
        # 提取文本内容
        text_content = self._extract_text_from_selector(original_selector)
        if not text_content:
            return original_selector
        
        print(f"🎯 修复点击选择器: {text_content}")
        
        # 1. 尝试智能导航选择器
        nav_info = get_smart_navigation_info(text_content)
        smart_selector = nav_info.get('smart_selector')
        
        if smart_selector:
            if await self._test_selector(smart_selector):
                print(f"✅ 智能导航修复成功: {smart_selector}")
                return smart_selector
        
        # 2. 尝试基于项目结构的选择器
        project_selectors = self._generate_project_based_selectors(text_content)
        for selector in project_selectors:
            if await self._test_selector(selector):
                print(f"✅ 项目结构修复成功: {selector}")
                return selector
        
        # 3. 尝试通用选择器
        generic_selectors = self._generate_generic_selectors(text_content)
        for selector in generic_selectors:
            if await self._test_selector(selector):
                print(f"✅ 通用选择器修复成功: {selector}")
                return selector
        
        # 4. 尝试模糊匹配
        fuzzy_selector = await self._fuzzy_match_selector(text_content)
        if fuzzy_selector:
            print(f"✅ 模糊匹配修复成功: {fuzzy_selector}")
            return fuzzy_selector
        
        print(f"❌ 选择器修复失败: {text_content}")
        return original_selector
    
    async def _fix_fill_selector(self, action: Dict) -> str:
        """修复填写选择器"""
        # 填写操作的修复逻辑
        return action.get('selector', '')
    
    def _extract_text_from_selector(self, selector: str) -> str:
        """从选择器中提取文本"""
        import re
        
        # text= 格式
        text_match = re.search(r'text=(.+)', selector)
        if text_match:
            return text_match.group(1).strip('"\'')
        
        # :has-text() 格式
        has_text_match = re.search(r':has-text\(["\']([^"\']+)["\']', selector)
        if has_text_match:
            return has_text_match.group(1)
        
        return ''
    
    def _generate_project_based_selectors(self, text: str) -> List[str]:
        """基于项目结构生成选择器"""
        selectors = []
        
        # 供应商模块特定选择器
        if '供应商' in text:
            selectors.extend([
                f'a[href*="/supplier"]:has-text("{text}")',
                f'.nav-link:has-text("{text}")',
                f'.sidebar a:has-text("{text}")'
            ])
        
        # 分类相关选择器
        if '分类' in text:
            selectors.extend([
                f'a[href*="/category"]:has-text("{text}")',
                f'.btn:has-text("{text}")',
                f'[data-toggle="modal"]:has-text("{text}")'
            ])
        
        # 产品相关选择器
        if '产品' in text:
            selectors.extend([
                f'a[href*="/product"]:has-text("{text}")',
                f'.btn-primary:has-text("{text}")',
                f'[onclick*="product"]:has-text("{text}")'
            ])
        
        # 操作按钮选择器
        if text in ['添加', '编辑', '删除', '查看', '提交', '取消']:
            selectors.extend([
                f'.btn:has-text("{text}")',
                f'button:has-text("{text}")',
                f'input[value="{text}"]',
                f'a.btn:has-text("{text}")'
            ])
        
        return selectors
    
    def _generate_generic_selectors(self, text: str) -> List[str]:
        """生成通用选择器"""
        return [
            f'text="{text}"',
            f'text={text}',
            f'button:has-text("{text}")',
            f'a:has-text("{text}")',
            f'[title="{text}"]',
            f'[alt="{text}"]',
            f'[value="{text}"]',
            f'*:has-text("{text}")'
        ]
    
    async def _test_selector(self, selector: str) -> bool:
        """测试选择器是否有效"""
        try:
            elements = await self.page.query_selector_all(selector)
            if not elements:
                return False
            
            # 检查是否有可见元素
            for element in elements:
                if await element.is_visible():
                    return True
            
            return False
        except Exception:
            return False
    
    async def _fuzzy_match_selector(self, target_text: str) -> str:
        """模糊匹配选择器"""
        try:
            # 获取页面上所有可能的元素
            elements = await self.page.query_selector_all('button, a, [role="button"], span, div, li')
            
            best_match = None
            best_score = 0
            
            for element in elements:
                if not await element.is_visible():
                    continue
                
                try:
                    text = await element.inner_text()
                    if not text:
                        text = await element.get_attribute('title') or ''
                    if not text:
                        text = await element.get_attribute('aria-label') or ''
                    
                    if text:
                        score = self._calculate_similarity(target_text.lower(), text.lower())
                        if score > best_score and score > 0.6:  # 相似度阈值
                            best_score = score
                            # 生成选择器
                            tag_name = await element.evaluate('el => el.tagName.toLowerCase()')
                            if tag_name in ['button', 'a']:
                                best_match = f'{tag_name}:has-text("{text}")'
                            else:
                                best_match = f'text="{text}"'
                
                except Exception:
                    continue
            
            return best_match
        
        except Exception:
            return None
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        if text1 == text2:
            return 1.0
        
        if text1 in text2 or text2 in text1:
            return 0.8
        
        # 计算共同词汇
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 or not words2:
            return 0.0
        
        common_words = words1.intersection(words2)
        return len(common_words) / max(len(words1), len(words2))

async def intelligent_fix_script(actions_file: str) -> str:
    """智能修复脚本的便捷函数"""
    async with IntelligentScriptFixer() as fixer:
        return await fixer.fix_script(actions_file)

# 测试函数
async def test_intelligent_fixer():
    """测试智能修复器"""
    actions_file = 'output/供应商模块视频录制脚本_带时间戳_actions.json'
    fixed_file = await intelligent_fix_script(actions_file)
    print(f"🎉 测试完成，修复文件: {fixed_file}")

if __name__ == "__main__":
    asyncio.run(test_intelligent_fixer())
