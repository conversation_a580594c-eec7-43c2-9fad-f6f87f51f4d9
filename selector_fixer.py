#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能选择器修复器
自动修复失效的选择器，提高脚本成功率
"""

import asyncio
import re
from typing import List, Dict, Optional, Tuple

class SelectorFixer:
    """智能选择器修复器"""
    
    def __init__(self, page):
        self.page = page
        
    async def fix_selector(self, original_selector: str, action_type: str = "click") -> Optional[str]:
        """修复单个选择器"""
        if not original_selector:
            return None
            
        # 首先尝试原始选择器
        if await self._test_selector(original_selector):
            return original_selector
        
        print(f"🔧 修复选择器: {original_selector}")
        
        # 提取文本内容
        text_content = self._extract_text_content(original_selector)
        if not text_content:
            return None
        
        # 生成候选选择器
        candidates = self._generate_selector_candidates(text_content, action_type)
        
        # 测试候选选择器
        for candidate in candidates:
            if await self._test_selector(candidate):
                print(f"✅ 修复成功: {original_selector} -> {candidate}")
                return candidate
        
        # 尝试模糊匹配
        fuzzy_match = await self._fuzzy_match_selector(text_content, action_type)
        if fuzzy_match:
            print(f"✅ 模糊匹配成功: {original_selector} -> {fuzzy_match}")
            return fuzzy_match
        
        print(f"❌ 修复失败: {original_selector}")
        return None
    
    def _extract_text_content(self, selector: str) -> Optional[str]:
        """从选择器中提取文本内容"""
        # text= 格式
        text_match = re.search(r'text=(.+)', selector)
        if text_match:
            return text_match.group(1).strip('"\'')
        
        # :has-text() 格式
        has_text_match = re.search(r':has-text\(["\']([^"\']+)["\']', selector)
        if has_text_match:
            return has_text_match.group(1)
        
        return None
    
    def _generate_selector_candidates(self, text: str, action_type: str) -> List[str]:
        """生成候选选择器"""
        candidates = []
        
        # 基础文本选择器
        candidates.extend([
            f'text={text}',
            f'text="{text}"',
            f"text='{text}'",
        ])
        
        # 元素类型 + 文本
        if action_type == "click":
            candidates.extend([
                f'button:has-text("{text}")',
                f'a:has-text("{text}")',
                f'[role="button"]:has-text("{text}")',
                f'span:has-text("{text}")',
                f'div:has-text("{text}")',
                f'li:has-text("{text}")',
            ])
        
        # 属性匹配
        candidates.extend([
            f'[title="{text}"]',
            f'[alt="{text}"]',
            f'[value="{text}"]',
            f'[aria-label="{text}"]',
            f'[data-text="{text}"]',
        ])
        
        # 包含匹配
        candidates.extend([
            f'[title*="{text}"]',
            f'[alt*="{text}"]',
            f'[aria-label*="{text}"]',
            f'*:has-text("{text}")',
        ])
        
        # 部分匹配（如果文本较长）
        if len(text) > 6:
            words = text.split()
            if len(words) > 1:
                for word in words:
                    if len(word) > 3:
                        candidates.extend([
                            f'text*="{word}"',
                            f'*:has-text("{word}")',
                            f'[title*="{word}"]',
                        ])
        
        return candidates
    
    async def _test_selector(self, selector: str) -> bool:
        """测试选择器是否有效"""
        try:
            elements = await self.page.query_selector_all(selector)
            if not elements:
                return False
            
            # 检查是否有可见元素
            for element in elements:
                if await element.is_visible():
                    return True
            
            return False
        except Exception:
            return False
    
    async def _fuzzy_match_selector(self, target_text: str, action_type: str) -> Optional[str]:
        """模糊匹配选择器"""
        try:
            # 获取页面上所有可能的元素
            if action_type == "click":
                elements = await self.page.query_selector_all('button, a, [role="button"], span, div, li')
            else:
                elements = await self.page.query_selector_all('input, textarea, select')
            
            best_match = None
            best_score = 0
            
            for element in elements:
                if not await element.is_visible():
                    continue
                
                # 获取元素文本
                try:
                    text = await element.inner_text()
                    if not text:
                        text = await element.get_attribute('title') or ''
                    if not text:
                        text = await element.get_attribute('aria-label') or ''
                    
                    if text:
                        score = self._calculate_similarity(target_text.lower(), text.lower())
                        if score > best_score and score > 0.6:  # 相似度阈值
                            best_score = score
                            # 生成选择器
                            tag_name = await element.evaluate('el => el.tagName.toLowerCase()')
                            if tag_name in ['button', 'a']:
                                best_match = f'{tag_name}:has-text("{text}")'
                            else:
                                best_match = f'text="{text}"'
                
                except Exception:
                    continue
            
            return best_match
        
        except Exception:
            return None
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        # 简单的相似度计算
        if text1 == text2:
            return 1.0
        
        if text1 in text2 or text2 in text1:
            return 0.8
        
        # 计算共同词汇
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 or not words2:
            return 0.0
        
        common_words = words1.intersection(words2)
        return len(common_words) / max(len(words1), len(words2))

async def batch_fix_selectors(page, actions: List[Dict]) -> List[Dict]:
    """批量修复选择器"""
    fixer = SelectorFixer(page)
    fixed_actions = []
    
    for action in actions:
        if action.get('action') in ['click', 'fill'] and action.get('selector'):
            original_selector = action['selector']
            fixed_selector = await fixer.fix_selector(original_selector, action['action'])
            
            if fixed_selector and fixed_selector != original_selector:
                action = action.copy()
                action['selector'] = fixed_selector
                action['original_selector'] = original_selector
        
        fixed_actions.append(action)
    
    return fixed_actions

# 测试函数
async def test_selector_fixer():
    """测试选择器修复功能"""
    from playwright.async_api import async_playwright
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        # 访问测试页面
        await page.goto("http://xiaoyuanst.com")
        await page.click("text=体验系统")
        
        # 跳过引导
        from guide_detector import smart_skip_guides
        await smart_skip_guides(page)
        
        # 测试选择器修复
        fixer = SelectorFixer(page)
        
        test_selectors = [
            "text=供应商分类",
            "text=添加分类", 
            "text=供应商列表",
            "text=添加供应商"
        ]
        
        print("🧪 测试选择器修复...")
        for selector in test_selectors:
            fixed = await fixer.fix_selector(selector)
            print(f"原始: {selector} -> 修复: {fixed}")
        
        await browser.close()

if __name__ == "__main__":
    asyncio.run(test_selector_fixer())
