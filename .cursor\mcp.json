{"mcpServers": {"studentscms-sqlserver": {"command": "node", "args": ["server.js"], "cwd": "C:\\StudentsCMSSP\\mcp-sqlserver", "env": {"MSSQL_SERVER": "**************", "MSSQL_DATABASE": "StudentsCMSSP", "MSSQL_USER": "StudentsCMSSP", "MSSQL_PASSWORD": "Xg2LS44Cyz5Zt8."}, "description": "SQL Server MCP for StudentsCMSSP database access and analysis"}, "studentscms-filesystem": {"command": "node", "args": ["server.js"], "cwd": "C:\\StudentsCMSSP\\mcp-servers\\filesystem-mcp", "description": "Secure filesystem operations for StudentsCMSSP project development"}, "studentscms-excel": {"command": "node", "args": ["server.js"], "cwd": "C:\\StudentsCMSSP\\mcp-servers\\excel-mcp", "description": "Excel file processing and financial report generation for StudentsCMSSP"}, "studentscms-pandoc": {"command": "node", "args": ["server.js"], "cwd": "C:\\StudentsCMSSP\\mcp-servers\\pandoc-mcp", "description": "Document format conversion and financial document templates"}, "studentscms-layout-optimizer": {"command": "node", "args": ["server.js"], "cwd": "C:\\StudentsCMSSP\\mcp-servers\\layout-optimizer-mcp", "description": "Web layout optimization and responsive design testing for StudentsCMSSP"}, "Playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"]}, "Context 7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "Sequential thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}}}