# 🎬 智慧食堂视频录制工具 - GUI版本

## 🚀 快速开始

### 启动方式

#### 方式1：使用启动器（推荐）
```bash
python start.py
```
然后选择 "1. 图形界面模式"

#### 方式2：直接启动GUI
```bash
python gui_app.py
```

## 📋 功能特性

### ✨ 主要功能
- 🖥️ **现代化图形界面** - 基于 tkinter 的友好用户界面
- 🔍 **环境自动检测** - 自动检测所需依赖项并提供安装指南
- 📝 **脚本选择器** - 可视化选择要录制的脚本文件
- ✅ **批量处理** - 支持同时录制多个脚本
- 📊 **实时日志** - 实时显示录制进度和状态
- 📁 **输出管理** - 一键打开输出目录

### 🔧 环境检测功能
GUI会自动检测以下依赖项：
- ✅ Python 环境
- ✅ edge-tts (语音合成)
- ✅ playwright (浏览器自动化)
- ✅ pydub (音频处理)
- ✅ ffmpeg (视频处理)

如果检测到缺少依赖项，会弹出详细的安装指南。

## 🎯 使用步骤

### 1. 启动程序
```bash
python start.py
```

### 2. 环境检测
- 程序启动后会自动检测运行环境
- 如果有缺少的依赖项，会显示安装指南
- 点击"重新检测"按钮可以重新检查环境

### 3. 选择脚本
- 在左侧"选择要录制的脚本"区域
- 勾选要录制的脚本文件
- 可以使用"全选"快速选择所有脚本

### 4. 智能录制（含预检测）
- 点击"智能录制(含预检测)"按钮
- 系统自动执行两阶段流程：
  - **阶段1**：脚本预检测和自动修复
  - **阶段2**：智能引导跳过和高质量录制
- 右侧日志区域会显示详细的录制进度
- 进度条显示当前状态

### 5. 查看结果
- 录制完成后，点击"打开输出目录"
- 生成的视频文件保存在 `output` 目录中

## 🎨 界面说明

### 左侧面板
- **环境检测区域** - 显示依赖项状态
- **脚本选择区域** - 选择要录制的脚本
- **控制按钮区域** - 开始/停止录制，打开输出目录

### 右侧面板
- **日志输出区域** - 实时显示录制日志
- **清空日志按钮** - 清空日志内容

### 底部
- **进度条** - 显示当前录制状态
- **状态文本** - 显示详细状态信息

## 🔧 依赖项安装指南

### edge-tts (语音合成)
```bash
pip install edge-tts
```

### playwright (浏览器自动化)
```bash
pip install playwright
playwright install
```

### pydub (音频处理)
```bash
pip install pydub
```

### ffmpeg (视频处理)
**Windows:**
1. 访问 https://ffmpeg.org/download.html
2. 下载 Windows 版本
3. 解压到任意目录
4. 将 ffmpeg.exe 所在目录添加到系统 PATH 环境变量

**或使用 chocolatey:**
```bash
choco install ffmpeg
```

## 🎯 高级功能

### 批量录制
- 可以同时选择多个脚本进行录制
- 程序会按顺序处理每个脚本
- 实时显示当前处理的脚本和总进度

### 日志管理
- 实时显示录制过程中的所有信息
- 支持不同级别的日志（信息、警告、错误）
- 可以清空日志内容重新开始

### 输出管理
- 自动创建 output 目录
- 一键打开输出目录查看结果
- 支持跨平台文件管理器打开

## 🐛 故障排除

### 常见问题

#### 1. 程序无法启动
- 确保已安装 Python 3.7+
- 确保已安装 tkinter（通常随 Python 一起安装）

#### 2. 环境检测失败
- 点击"重新检测"按钮
- 按照弹出的安装指南安装缺少的依赖项
- 重启程序

#### 3. 录制失败
- 检查网络连接
- 确保目标网站可以正常访问
- 查看日志输出中的错误信息

#### 4. 音频播放权限错误
- 这是正常现象，程序会跳过本地音频播放
- 最终视频仍会包含音频（通过 ffmpeg 合成）

### 获取帮助
如果遇到问题：
1. 查看日志输出中的详细错误信息
2. 确保所有依赖项都已正确安装
3. 检查脚本文件格式是否正确

## 🎉 享受使用！

现在你可以通过友好的图形界面轻松录制智慧食堂演示视频了！

如果你更喜欢命令行模式，仍然可以直接运行：
```bash
python main.py
```
