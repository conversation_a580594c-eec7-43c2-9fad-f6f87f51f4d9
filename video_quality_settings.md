# 📹 视频录制质量优化配置

## 🎯 当前优化设置

### 📊 录制参数
- **分辨率**：1920x1080 (Full HD)
- **帧率**：25fps (Playwright 默认)
- **浏览器窗口**：1920x1080 (全屏录制)
- **原始格式**：WebM (VP8编码)

### 🔧 后期处理参数
- **最终编码**：H.264 (libx264)
- **质量因子**：CRF 18 (高质量)
- **编码预设**：medium (平衡质量和速度)
- **音频编码**：AAC
- **音频码率**：192kbps
- **音频采样率**：48kHz
- **声道**：立体声

## 📈 质量对比

### 🔴 优化前
- 分辨率：800x450
- 码率：~450kbps
- 编码：VP8
- 音频：无

### 🟢 优化后
- 分辨率：1920x1080
- 码率：自适应高质量
- 编码：H.264
- 音频：192kbps AAC立体声

## ⚙️ 进一步优化选项

### 🎬 如需更高质量
```python
# 在 merge_audio_video 函数中修改 cmd 参数
cmd = [
    "ffmpeg", "-y",
    "-i", video_path,
    "-i", audio_path,
    "-c:v", "libx264",
    "-preset", "slow",      # 更慢但质量更好
    "-crf", "15",           # 更高质量 (15 vs 18)
    "-c:a", "aac",
    "-b:a", "256k",         # 更高音频码率
    "-ar", "48000",
    "-ac", "2",
    "-movflags", "+faststart",
    "-shortest",
    output_path
]
```

### 🚀 如需更快处理
```python
# 在 merge_audio_video 函数中修改 cmd 参数
cmd = [
    "ffmpeg", "-y",
    "-i", video_path,
    "-i", audio_path,
    "-c:v", "libx264",
    "-preset", "fast",      # 更快处理
    "-crf", "23",           # 稍低质量但更快
    "-c:a", "aac",
    "-b:a", "128k",         # 标准音频码率
    "-ar", "44100",         # 标准采样率
    "-ac", "2",
    "-shortest",
    output_path
]
```

### 📱 如需移动端优化
```python
# 在 browser.new_context 中修改参数
context = await browser.new_context(
    record_video_dir="output",
    record_video_size={"width": 1280, "height": 720},  # 720p
    viewport={"width": 1280, "height": 720}
)
```

## 🎯 推荐配置

### 📺 演示视频 (当前配置)
- **用途**：产品演示、教学视频
- **分辨率**：1920x1080
- **质量**：高质量 (CRF 18)
- **文件大小**：中等

### 🎓 培训视频
- **分辨率**：1920x1080
- **质量**：超高质量 (CRF 15)
- **音频**：256kbps
- **适用**：详细教学、技术培训

### 📱 快速预览
- **分辨率**：1280x720
- **质量**：标准质量 (CRF 23)
- **音频**：128kbps
- **适用**：快速演示、内部预览

## 🔍 质量检查命令

### 检查视频信息
```bash
ffprobe -v quiet -print_format json -show_format -show_streams video.mp4
```

### 检查视频质量
```bash
ffplay video.mp4  # 播放检查
```

### 文件大小对比
```bash
ls -lh output/*.mp4  # 查看文件大小
```

## 📋 注意事项

1. **性能影响**：高质量设置会增加处理时间
2. **存储空间**：高质量视频文件更大
3. **网络传输**：考虑最终使用场景选择合适质量
4. **硬件要求**：确保有足够的CPU和内存进行编码

## 🎉 预期效果

使用优化后的配置，你将获得：
- ✅ **清晰度提升**：从 800x450 提升到 1920x1080
- ✅ **音频质量**：192kbps AAC 立体声
- ✅ **兼容性**：H.264 编码，广泛支持
- ✅ **专业效果**：适合商业演示和培训使用
