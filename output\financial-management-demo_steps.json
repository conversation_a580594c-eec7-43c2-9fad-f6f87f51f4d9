[{"narration": "首先我们登录智慧食堂平台。为了方便演示，我使用游客账号登录，这样您也可以跟着一起操作体验。", "action": "goto", "selector": "", "value": "xiaoyuanst.com"}, {"narration": "首先我们登录智慧食堂平台。为了方便演示，我使用游客账号登录，这样您也可以跟着一起操作体验。", "action": "click", "selector": "text=体"}, {"narration": "现在我们进入财务管理模块。点击顶部的财务管理菜单，可以看到系统提供了12个专业的财务管理功能模块。这个架构设计完全基于现代财务管理的需求：基础设置包括财务概览和会计科目；日常业务包括财务凭证、应付账款和付款记录；查询统计包括各类账务查询；财务报表包括资产负债表、成本分析和账龄分析。这样的功能架构确保了财务管理的完整性和专业性。", "action": "click", "selector": "text=顶"}, {"narration": "现在我们进入财务管理模块。点击顶部的财务管理菜单，可以看到系统提供了12个专业的财务管理功能模块。这个架构设计完全基于现代财务管理的需求：基础设置包括财务概览和会计科目；日常业务包括财务凭证、应付账款和付款记录；查询统计包括各类账务查询；财务报表包括资产负债表、成本分析和账龄分析。这样的功能架构确保了财务管理的完整性和专业性。", "action": "highlight", "selector": ""}, {"narration": "我们点击会计科目进入科目管理页面。这里您可以看到完整的会计科目树状结构。系统科目是标准化的会计科目，完全符合会计准则，包括资产类、负债类、所有者权益类、成本类、损益类等。这些科目是全局共享的，确保了会计处理的规范性。", "action": "click", "selector": "text=会"}, {"narration": "我们点击会计科目进入科目管理页面。这里您可以看到完整的会计科目树状结构。系统科目是标准化的会计科目，完全符合会计准则，包括资产类、负债类、所有者权益类、成本类、损益类等。这些科目是全局共享的，确保了会计处理的规范性。", "action": "highlight", "selector": ""}, {"narration": "这是财务凭证管理页面。页面顶部显示了重要的统计信息：当前共有6张凭证，待审核0张，已审核6张，本月金额142,748.20元。这些数据让财务人员能够快速了解凭证处理状况和资金流动情况。", "action": "click", "selector": "text=财"}, {"narration": "这是财务凭证管理页面。页面顶部显示了重要的统计信息：当前共有6张凭证，待审核0张，已审核6张，本月金额142,748.20元。这些数据让财务人员能够快速了解凭证处理状况和资金流动情况。", "action": "highlight", "selector": ""}, {"narration": "在应付账款管理页面，系统提供了全面的应付款管理功能。首先是应付款的登记，系统可以根据采购订单和入库单自动生成应付款记录，也支持手工录入。每笔应付款都包含供应商信息、应付金额、到期日期等关键信息。", "action": "click", "selector": "text=应"}, {"narration": "在应付账款管理页面，系统提供了全面的应付款管理功能。首先是应付款的登记，系统可以根据采购订单和入库单自动生成应付款记录，也支持手工录入。每笔应付款都包含供应商信息、应付金额、到期日期等关键信息。", "action": "highlight", "selector": ""}]