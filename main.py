import os
import re
import json
import glob
import asyncio
import sys
import time
from pydub import AudioSegment
import edge_tts

VIDEO_SCRIPTS_DIR = "video-scripts"
OUTPUT_DIR = "output"
ERROR_LOG = os.path.join(OUTPUT_DIR, "error.log")

def log(msg):
    print(msg)
    with open(ERROR_LOG, "a", encoding="utf-8") as f:
        f.write(msg + "\n")

def check_dependencies():
    try:
        import edge_tts
    except ImportError:
        print("缺少 edge-tts，请先运行: pip install edge-tts")
        sys.exit(1)
    try:
        import playwright
    except ImportError:
        print("缺少 playwright，请先运行: pip install playwright && playwright install")
        sys.exit(1)

def unique_name(base, ext):
    ts = time.strftime("%Y%m%d_%H%M%S")
    return f"{base}_{ts}.{ext}"

# 修复后的智能解析：分段结构自动配对
# 支持多行操作和多行解说词

def guess_action_and_selector(op_line):
    op_line = op_line.strip()
    # 修复后的规则：
    if op_line.startswith("点击"):
        # 修复：更准确地提取按钮文本
        # 优先匹配最后一个引号内的文本（这是真正的按钮文本）
        matches = re.findall(r'["\"]([^"\"]+)["\"]', op_line)
        if matches:
            btn_text = matches[-1]  # 取最后一个匹配项
            return "click", f"text={btn_text}"

        # 如果没有引号，尝试提取关键词
        m = re.search(r'点击(.+?)(?:按钮|，|。|$)', op_line)
        if m:
            btn_text = m.group(1).strip()
            # 清理描述性文字
            btn_text = re.sub(r'(供应商列表中的|下拉框|按钮)', '', btn_text).strip()
            if btn_text:
                return "click", f"text={btn_text}"

        return "click", ""  # fallback
    elif op_line.startswith("输入"):
        # 如: 输入供应商名称"张三"
        m = re.search(r'输入(.+?)"(.+?)"', op_line)
        if m:
            field, value = m.group(1).strip(), m.group(2).strip()
            # selector 简单用 field 关键字
            return "fill", f"input[placeholder*='{field}']", value
        else:
            return "fill", "input", ""
    elif op_line.startswith("选择"):
        m = re.search(r'选择["\"]([^"\"]+)["\"]', op_line)
        if m:
            return "click", f"text={m.group(1)}"
        else:
            return "click", ""
    elif op_line.startswith("在") and "输入" in op_line:
        # 如: 在关键词搜索框输入"蔬菜"
        m = re.search(r'在(.+?)输入["\"]([^"\"]+)["\"]', op_line)
        if m:
            field, value = m.group(1).strip(), m.group(2).strip()
            # 改进选择器，使用更通用的方式
            if "搜索" in field:
                return "fill", "input[type='text'], input[placeholder*='搜索']", value
            else:
                return "fill", f"input[placeholder*='{field}'], input", value
        else:
            return "fill", "input", ""
    elif op_line.startswith("访问"):
        m = re.search(r'访问\s*([a-zA-Z0-9:\./_-]+)', op_line)
        if m:
            return "goto", "", m.group(1)
        else:
            return "goto", "", ""
    elif op_line.startswith("展示") or op_line.startswith("指向"):
        return "wait", "", "2000"
    else:
        return "wait", "", "1000"

def extract_steps_from_md(md_path):
    """智能步骤提取函数，支持多种格式，自动添加登录步骤"""
    with open(md_path, encoding="utf-8") as f:
        content = f.read()

    # 方法1：尝试解析标准格式（**操作：** 和 **解说词：**）
    standard_steps = _extract_standard_format(content)
    if standard_steps:
        return _add_login_steps(standard_steps)

    # 方法2：尝试解析带时间戳格式
    timestamp_steps = _extract_timestamp_format(content)
    if timestamp_steps:
        return _add_login_steps(timestamp_steps)

    # 方法3：返回空列表
    return []

def _add_login_steps(steps):
    """自动添加登录步骤到脚本开头"""
    # 检查是否已经包含登录步骤
    has_login = False
    for step in steps[:3]:  # 检查前3个步骤
        operation = step.get('operation', '').lower()
        if any(keyword in operation for keyword in ['访问', 'xiaoyuanst.com', '体验系统', '游客']):
            has_login = True
            break

    if has_login:
        return steps  # 已有登录步骤，直接返回

    # 创建标准登录步骤
    login_steps = [
        {
            "narration": "首先我们访问智慧食堂平台。我将使用游客账号登录，这样您也可以跟着一起操作体验。",
            "action": "goto",
            "selector": "",
            "operation": "访问 xiaoyuanst.com",
            "value": "http://xiaoyuanst.com"
        },
        {
            "narration": "现在我们点击体验系统按钮，以游客身份进入系统。登录成功后，我们可以看到系统的主界面。",
            "action": "click",
            "selector": "text=体验系统",
            "operation": "点击\"体验系统\"进行游客登录"
        },
        {
            "narration": "系统加载完成，现在我们需要跳过新手引导，以便正常使用左侧菜单功能。",
            "action": "wait",
            "selector": "",
            "operation": "等待页面加载完成",
            "value": "3000"
        },
        {
            "narration": "我们点击跳过按钮，跳过新手引导步骤，这样就可以自由操作系统的各个功能模块了。",
            "action": "click_skip_guide",
            "selector": "text=跳过",
            "operation": "点击\"跳过\"按钮跳过引导"
        },
        {
            "narration": "引导已跳过，现在我们可以看到完整的系统界面，左侧菜单已经可以正常使用。接下来我们开始演示具体功能。",
            "action": "wait",
            "selector": "",
            "operation": "等待引导跳过完成",
            "value": "2000"
        }
    ]

    # 将登录步骤添加到开头
    return login_steps + steps

def _extract_standard_format(content):
    """解析标准格式：**操作：** 和 **解说词：**"""
    lines = content.split('\n')
    steps = []
    i = 0

    while i < len(lines):
        line = lines[i].strip()

        if line.startswith("**操作：**"):
            # 收集所有操作行
            op_lines = []
            i += 1
            while i < len(lines) and (lines[i].strip().startswith("-") or lines[i].strip() == ""):
                if lines[i].strip().startswith("-"):
                    op_lines.append(lines[i].strip()[1:].strip())
                i += 1

            # 查找对应的解说词
            narration = ""
            while i < len(lines):
                narr_line = lines[i].strip()
                if narr_line.startswith("**解说词：**"):
                    narration = narr_line.replace("**解说词：**", "").strip().strip('"')
                    if not narration and i+1 < len(lines):
                        next_line = lines[i+1].strip().strip('"')
                        if next_line and not next_line.startswith("**"):
                            narration = next_line
                            i += 1
                    break
                i += 1

            # 为每个操作创建独立的步骤
            for op in op_lines:
                action, selector, *value = guess_action_and_selector(op)
                step = {
                    "narration": narration,
                    "action": action,
                    "selector": selector,
                    "operation": op
                }
                if value and value[0]:
                    step["value"] = value[0]
                steps.append(step)
        else:
            i += 1

    return steps

def _extract_timestamp_format(content):
    """解析带时间戳格式"""
    steps = []

    # 查找所有操作步骤部分
    sections = re.findall(r'### \d+\.\d+.*?\[.*?\].*?(?=###|\Z)', content, re.DOTALL)

    for section in sections:
        # 提取解说词
        narration_match = re.search(r'\*\*解说词\*\*:\s*"([^"]+)"', section)
        narration = narration_match.group(1) if narration_match else ""

        # 提取操作步骤
        operations = re.findall(r'- \[[\d:.-]+\] (.+)', section)

        for op in operations:
            # 清理操作文本，过滤掉纯展示性操作
            if any(skip in op for skip in ['展示', '观察', '介绍', '说明']):
                continue

            # 处理选择操作，提取具体的选择内容
            if op.startswith('选择'):
                # 尝试多种模式提取选择内容
                patterns = [
                    r'选择(.+?)[:：]?\s*["\"]([^"\"]+)["\"]',  # 选择XX: "内容"
                    r'选择["\"]([^"\"]+)["\"]',                # 选择"内容"
                    r'选择(.+?)[:：]\s*(.+)',                 # 选择XX: 内容
                    r'选择\s*(.+)',                          # 选择 内容
                ]

                matched = False
                for pattern in patterns:
                    select_match = re.search(pattern, op)
                    if select_match:
                        if len(select_match.groups()) >= 2:
                            value = select_match.group(2).strip()
                        else:
                            value = select_match.group(1).strip()

                        # 清理选择内容
                        value = value.strip('"\'""''')

                        if value:
                            step = {
                                "narration": narration,
                                "action": "click",
                                "selector": f"text={value}",
                                "operation": op
                            }
                            steps.append(step)
                            matched = True
                            break

                if matched:
                    continue
                else:
                    # 如果无法提取具体内容，创建一个等待操作
                    step = {
                        "narration": narration,
                        "action": "wait",
                        "selector": "",
                        "operation": op,
                        "value": "2000"  # 等待2秒
                    }
                    steps.append(step)
                    continue

            action, selector, *value = guess_action_and_selector(op)

            # 跳过空选择器的点击操作
            if action == "click" and not selector:
                print(f"⚠️ 跳过空选择器操作: {op}")
                continue

            if action != "wait" or selector:  # 只保留有意义的操作
                step = {
                    "narration": narration,
                    "action": action,
                    "selector": selector,
                    "operation": op
                }
                if value and value[0]:
                    step["value"] = value[0]
                steps.append(step)

    return steps

async def tts(text, mp3_path):
    communicate = edge_tts.Communicate(text, "zh-CN-XiaoxiaoNeural")
    await communicate.save(mp3_path)

# 旧的batch_tts函数已被smart_batch_tts替代

def generate_actions_json(steps, base_name):
    actions = []
    for idx, step in enumerate(steps, 1):
        action_obj = {
            "step": idx,
            "narration": step["narration"],
            "audio": f"{base_name}_step{idx}.mp3",
            "action": step["action"],
            "selector": step["selector"]
        }
        if step.get("value"):
            action_obj["value"] = step["value"]
        actions.append(action_obj)
    json_path = os.path.join(OUTPUT_DIR, f"{base_name}_actions.json")
    with open(json_path, "w", encoding="utf-8") as f:
        json.dump(actions, f, ensure_ascii=False, indent=2)
    return json_path

async def process_one_md(md_path):
    """智能处理单个脚本文件"""
    base_name = os.path.splitext(os.path.basename(md_path))[0]
    steps = extract_steps_from_md(md_path)

    # 保存解析的步骤
    steps_json_path = os.path.join(OUTPUT_DIR, f"{base_name}_steps.json")
    with open(steps_json_path, "w", encoding="utf-8") as f:
        json.dump(steps, f, ensure_ascii=False, indent=2)

    if not steps:
        log(f"未在 {md_path} 中找到有效步骤，跳过。")
        return

    log(f"正在处理: {base_name} (共{len(steps)}个步骤)")

    try:
        # 智能音频生成：只生成缺失或损坏的音频文件
        await smart_batch_tts(steps, base_name)

        # 生成操作配置文件
        actions_json = generate_actions_json(steps, base_name)

        # 显示解析结果用于调试
        print(f"\n=== {base_name} 解析结果 ===")
        for idx, step in enumerate(steps, 1):
            operation = step.get('operation', step.get('narration', '')[:30] + '...')
            print(f"步骤{idx}: {operation} -> {step['action']}({step['selector']})")

        # 录制视频
        mp4_path = os.path.join(OUTPUT_DIR, unique_name(base_name, 'mp4'))
        ret = os.system(f"python run_playwright.py {actions_json} {mp4_path}")

        if ret != 0:
            log(f"录制 {base_name} 失败，返回码: {ret}")
        else:
            log(f"录制完成: {mp4_path}")

    except Exception as e:
        log(f"处理 {base_name} 失败: {e}")

async def smart_batch_tts(steps, base_name):
    """智能批量生成TTS音频：只生成需要的文件"""
    log(f"检查音频文件状态...")

    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    for idx, step in enumerate(steps, 1):
        mp3_path = os.path.join(OUTPUT_DIR, f"{base_name}_step{idx}.mp3")

        # 使用绝对路径进行检查
        abs_mp3_path = os.path.abspath(mp3_path)

        # 检查是否需要生成音频
        need_generate = False

        if not os.path.exists(abs_mp3_path):
            need_generate = True
            reason = "文件不存在"
            log(f"🔍 检查路径: {abs_mp3_path} - 文件不存在")
        else:
            # 检查文件大小
            try:
                file_size = os.path.getsize(abs_mp3_path)
                if file_size < 1024:  # 小于1KB可能损坏
                    need_generate = True
                    reason = f"文件损坏 (大小: {file_size} bytes)"
                else:
                    log(f"✓ 音频文件正常: step{idx}.mp3 (大小: {file_size} bytes)")
            except Exception as e:
                need_generate = True
                reason = f"文件检查失败: {e}"

        if need_generate:
            log(f"🎵 生成音频: step{idx}.mp3 ({reason})")
            try:
                # 确保目录存在
                os.makedirs(os.path.dirname(abs_mp3_path), exist_ok=True)

                # 检查解说词内容
                narration = step.get("narration", "").strip()
                if not narration:
                    log(f"⚠️ 步骤{idx}没有解说词，跳过音频生成")
                    continue

                # 生成音频
                await tts(narration, abs_mp3_path)

                # 等待文件写入完成
                import time
                time.sleep(0.5)

                # 验证生成的文件
                if os.path.exists(abs_mp3_path):
                    file_size = os.path.getsize(abs_mp3_path)
                    if file_size > 1024:
                        log(f"✓ 音频生成成功: step{idx}.mp3 ({file_size} bytes)")
                    else:
                        log(f"⚠️ 音频文件过小: step{idx}.mp3 ({file_size} bytes)")
                        # 尝试重新生成
                        if os.path.exists(abs_mp3_path):
                            os.remove(abs_mp3_path)
                        await tts(narration, abs_mp3_path)
                        time.sleep(1)
                        if os.path.exists(abs_mp3_path) and os.path.getsize(abs_mp3_path) > 1024:
                            log(f"✓ 重新生成成功: step{idx}.mp3")
                        else:
                            log(f"❌ 重新生成仍失败: step{idx}.mp3")
                else:
                    log(f"❌ 音频文件未生成: step{idx}.mp3")
            except Exception as e:
                log(f"❌ 音频生成失败: step{idx}.mp3 - {e}")

    log(f"音频文件检查完成")

def main():
    check_dependencies()
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    if os.path.exists(ERROR_LOG):
        os.remove(ERROR_LOG)
    md_files = glob.glob(os.path.join(VIDEO_SCRIPTS_DIR, "*.md"))
    if not md_files:
        print("未找到任何 markdown 脚本。")
        return
    print(f"共检测到 {len(md_files)} 个模块，开始自动化处理...")
    loop = asyncio.get_event_loop()
    tasks = [process_one_md(md) for md in md_files]
    loop.run_until_complete(asyncio.gather(*tasks))
    print("全部处理完成！如有错误请查看 output/error.log")
    # 自动打开 output 目录
    if sys.platform.startswith("win"):
        os.system(f"start {OUTPUT_DIR}")
    else:
        os.system(f"open {OUTPUT_DIR}")

if __name__ == "__main__":
    main()
