import os
import re
import json
import glob
import asyncio
import sys
import time
from pydub import AudioSegment
import edge_tts

VIDEO_SCRIPTS_DIR = "video-scripts"
OUTPUT_DIR = "output"
ERROR_LOG = os.path.join(OUTPUT_DIR, "error.log")

def log(msg):
    print(msg)
    with open(ERROR_LOG, "a", encoding="utf-8") as f:
        f.write(msg + "\n")

def check_dependencies():
    try:
        import edge_tts
    except ImportError:
        print("缺少 edge-tts，请先运行: pip install edge-tts")
        sys.exit(1)
    try:
        import playwright
    except ImportError:
        print("缺少 playwright，请先运行: pip install playwright && playwright install")
        sys.exit(1)

def unique_name(base, ext):
    ts = time.strftime("%Y%m%d_%H%M%S")
    return f"{base}_{ts}.{ext}"

# 修复后的智能解析：分段结构自动配对
# 支持多行操作和多行解说词

def guess_action_and_selector(op_line):
    op_line = op_line.strip()
    # 修复后的规则：
    if op_line.startswith("点击"):
        # 修复：更准确地提取按钮文本
        # 优先匹配最后一个引号内的文本（这是真正的按钮文本）
        matches = re.findall(r'["\"]([^"\"]+)["\"]', op_line)
        if matches:
            btn_text = matches[-1]  # 取最后一个匹配项
            return "click", f"text={btn_text}"
        else:
            return "click", ""  # fallback
    elif op_line.startswith("输入"):
        # 如: 输入供应商名称"张三"
        m = re.search(r'输入(.+?)"(.+?)"', op_line)
        if m:
            field, value = m.group(1).strip(), m.group(2).strip()
            # selector 简单用 field 关键字
            return "fill", f"input[placeholder*='{field}']", value
        else:
            return "fill", "input", ""
    elif op_line.startswith("选择"):
        m = re.search(r'选择["\"]([^"\"]+)["\"]', op_line)
        if m:
            return "click", f"text={m.group(1)}"
        else:
            return "click", ""
    elif op_line.startswith("在") and "输入" in op_line:
        # 如: 在关键词搜索框输入"蔬菜"
        m = re.search(r'在(.+?)输入["\"]([^"\"]+)["\"]', op_line)
        if m:
            field, value = m.group(1).strip(), m.group(2).strip()
            return "fill", f"input[placeholder*='{field}']", value
        else:
            return "fill", "input", ""
    elif op_line.startswith("访问"):
        m = re.search(r'访问\s*([a-zA-Z0-9:\./_-]+)', op_line)
        if m:
            return "goto", "", m.group(1)
        else:
            return "goto", "", ""
    elif op_line.startswith("展示") or op_line.startswith("指向"):
        return "wait", "", "2000"
    else:
        return "wait", "", "1000"

def extract_steps_from_md(md_path):
    with open(md_path, encoding="utf-8") as f:
        lines = f.readlines()
    steps = []
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        if line.startswith("**操作：**"):
            # 收集所有操作行
            op_lines = []
            i += 1
            while i < len(lines) and (lines[i].strip().startswith("-") or lines[i].strip() == ""):
                if lines[i].strip().startswith("-"):
                    op_lines.append(lines[i].strip()[1:].strip())
                i += 1
            # 查找下一个解说词
            narration = ""
            while i < len(lines):
                narr_line = lines[i].strip()
                if narr_line.startswith("**解说词：**"):
                    narration = narr_line.replace("**解说词：**", "").strip().strip('"')
                    if not narration and i+1 < len(lines):
                        narration = lines[i+1].strip().strip('"')
                        i += 1
                    break
                i += 1
            # 每个操作都配对 narration
            for op in op_lines:
                action, selector, *value = guess_action_and_selector(op)
                step = {
                    "narration": narration,
                    "action": action,
                    "selector": selector
                }
                if value and value[0]:
                    step["value"] = value[0]
                steps.append(step)
        else:
            i += 1
    return steps

async def tts(text, mp3_path):
    communicate = edge_tts.Communicate(text, "zh-CN-XiaoxiaoNeural")
    await communicate.save(mp3_path)

async def batch_tts(steps, base_name):
    mp3_paths = []
    for idx, step in enumerate(steps, 1):
        mp3_path = os.path.join(OUTPUT_DIR, f"{base_name}_step{idx}.mp3")
        if not os.path.exists(mp3_path):
            await tts(step["narration"], mp3_path)
        mp3_paths.append(mp3_path)
    return mp3_paths

def generate_actions_json(steps, base_name):
    actions = []
    for idx, step in enumerate(steps, 1):
        action_obj = {
            "step": idx,
            "narration": step["narration"],
            "audio": f"{base_name}_step{idx}.mp3",
            "action": step["action"],
            "selector": step["selector"]
        }
        if step.get("value"):
            action_obj["value"] = step["value"]
        actions.append(action_obj)
    json_path = os.path.join(OUTPUT_DIR, f"{base_name}_actions.json")
    with open(json_path, "w", encoding="utf-8") as f:
        json.dump(actions, f, ensure_ascii=False, indent=2)
    return json_path

async def process_one_md(md_path):
    base_name = os.path.splitext(os.path.basename(md_path))[0]
    steps = extract_steps_from_md(md_path)
    # 新增：保存有效步骤到 steps.json
    steps_json_path = os.path.join(OUTPUT_DIR, f"{base_name}_steps.json")
    with open(steps_json_path, "w", encoding="utf-8") as f:
        json.dump(steps, f, ensure_ascii=False, indent=2)
    if not steps:
        log(f"未在 {md_path} 中找到有效步骤，跳过。")
        return
    log(f"正在处理: {base_name}")
    try:
        await batch_tts(steps, base_name)
        actions_json = generate_actions_json(steps, base_name)
        mp4_path = os.path.join(OUTPUT_DIR, unique_name(base_name, 'mp4'))
        ret = os.system(f"python run_playwright.py {actions_json} {mp4_path}")
        if ret != 0:
            log(f"录制 {base_name} 失败，返回码: {ret}")
        else:
            log(f"录制完成: {mp4_path}")
    except Exception as e:
        log(f"处理 {base_name} 失败: {e}")

def main():
    check_dependencies()
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    if os.path.exists(ERROR_LOG):
        os.remove(ERROR_LOG)
    md_files = glob.glob(os.path.join(VIDEO_SCRIPTS_DIR, "*.md"))
    if not md_files:
        print("未找到任何 markdown 脚本。")
        return
    print(f"共检测到 {len(md_files)} 个模块，开始自动化处理...")
    loop = asyncio.get_event_loop()
    tasks = [process_one_md(md) for md in md_files]
    loop.run_until_complete(asyncio.gather(*tasks))
    print("全部处理完成！如有错误请查看 output/error.log")
    # 自动打开 output 目录
    if sys.platform.startswith("win"):
        os.system(f"start {OUTPUT_DIR}")
    else:
        os.system(f"open {OUTPUT_DIR}")

if __name__ == "__main__":
    main()
