#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单模块测试脚本：只测试一个模块的录制
"""

import os
import asyncio
import sys
from main_fixed import extract_steps_from_md, batch_tts, generate_actions_json, unique_name

OUTPUT_DIR = "output"

async def test_single_module():
    """测试单个模块的完整流程"""
    
    # 选择一个简单的模块进行测试
    test_file = "video-scripts/02-stock-in-management-demo.md"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return
    
    print(f"🧪 测试模块: {test_file}")
    print("=" * 50)
    
    # 1. 解析步骤
    steps = extract_steps_from_md(test_file)
    print(f"✅ 解析到 {len(steps)} 个步骤")
    
    # 2. 显示解析结果
    print("\n📋 解析结果:")
    for i, step in enumerate(steps, 1):
        print(f"  {i}. {step.get('operation', 'N/A')}")
        print(f"     -> {step['action']}({step['selector']})")
        if step.get('value'):
            print(f"     -> 值: {step['value']}")
    
    # 3. 生成音频（跳过，避免权限问题）
    print(f"\n⏭️  跳过音频生成（避免权限问题）")
    
    # 4. 生成 actions.json
    base_name = "test-single-module"
    actions_json = generate_actions_json(steps, base_name)
    print(f"✅ 生成配置文件: {actions_json}")
    
    # 5. 显示生成的配置
    import json
    with open(actions_json, 'r', encoding='utf-8') as f:
        actions = json.load(f)
    
    print(f"\n📄 生成的配置文件内容:")
    for action in actions[:3]:  # 只显示前3个
        print(f"  步骤{action['step']}: {action['action']}({action['selector']})")
    
    print(f"\n🎉 测试完成！配置文件已生成: {actions_json}")
    print(f"💡 如需录制视频，运行: python run_playwright.py {actions_json} test_output.mp4")

if __name__ == "__main__":
    asyncio.run(test_single_module())
