"""
响应助手工具类
提供统一的响应格式和处理方法
"""

from flask import jsonify, flash, redirect, url_for, request, current_app
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, date
from decimal import Decimal


class ResponseHelper:
    """响应格式化助手类"""
    
    @staticmethod
    def success_response(message: str, data: Any = None, **kwargs) -> dict:
        """
        成功响应格式
        
        Args:
            message: 成功消息
            data: 返回数据
            **kwargs: 额外的响应字段
            
        Returns:
            JSON响应字典
        """
        response = {
            'success': True,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
        
        if data is not None:
            response['data'] = ResponseHelper._serialize_data(data)
        
        # 添加额外字段
        response.update(kwargs)
        
        return jsonify(response)
    
    @staticmethod
    def error_response(message: str, code: int = 400, details: Dict[str, Any] = None, **kwargs) -> tuple:
        """
        错误响应格式
        
        Args:
            message: 错误消息
            code: HTTP状态码
            details: 错误详情
            **kwargs: 额外的响应字段
            
        Returns:
            (JSON响应, HTTP状态码)
        """
        response = {
            'success': False,
            'message': message,
            'error_code': code,
            'timestamp': datetime.now().isoformat()
        }
        
        if details:
            response['details'] = details
        
        # 添加额外字段
        response.update(kwargs)
        
        return jsonify(response), code
    
    @staticmethod
    def paginated_response(data: List[Any], page: int, per_page: int, 
                          total: int, message: str = "查询成功") -> dict:
        """
        分页响应格式
        
        Args:
            data: 数据列表
            page: 当前页码
            per_page: 每页数量
            total: 总记录数
            message: 响应消息
            
        Returns:
            JSON响应字典
        """
        total_pages = (total + per_page - 1) // per_page
        
        pagination_info = {
            'page': page,
            'per_page': per_page,
            'total': total,
            'total_pages': total_pages,
            'has_prev': page > 1,
            'has_next': page < total_pages,
            'prev_page': page - 1 if page > 1 else None,
            'next_page': page + 1 if page < total_pages else None
        }
        
        return ResponseHelper.success_response(
            message=message,
            data=ResponseHelper._serialize_data(data),
            pagination=pagination_info
        )
    
    @staticmethod
    def validation_error_response(errors: Dict[str, List[str]]) -> tuple:
        """
        验证错误响应格式
        
        Args:
            errors: 验证错误字典 {字段名: [错误消息列表]}
            
        Returns:
            (JSON响应, HTTP状态码)
        """
        return ResponseHelper.error_response(
            message="数据验证失败",
            code=422,
            details={'validation_errors': errors}
        )
    
    @staticmethod
    def flash_and_redirect(message: str, category: str = 'success', 
                          endpoint: str = None, **kwargs) -> Any:
        """
        Flash消息并重定向
        
        Args:
            message: Flash消息
            category: 消息类别 ('success', 'danger', 'warning', 'info')
            endpoint: 重定向端点
            **kwargs: url_for的额外参数
            
        Returns:
            重定向响应
        """
        flash(message, category)
        
        if endpoint:
            return redirect(url_for(endpoint, **kwargs))
        else:
            # 默认重定向到来源页面或首页
            return redirect(request.referrer or url_for('main.index'))
    
    @staticmethod
    def _serialize_data(data: Any) -> Any:
        """
        序列化数据，处理特殊类型
        
        Args:
            data: 要序列化的数据
            
        Returns:
            序列化后的数据
        """
        if data is None:
            return None
        elif isinstance(data, (datetime, date)):
            return data.isoformat()
        elif isinstance(data, Decimal):
            return float(data)
        elif isinstance(data, list):
            return [ResponseHelper._serialize_data(item) for item in data]
        elif isinstance(data, dict):
            return {key: ResponseHelper._serialize_data(value) for key, value in data.items()}
        elif hasattr(data, '__dict__'):
            # 处理对象（如SQLAlchemy模型）
            return ResponseHelper._serialize_object(data)
        else:
            return data
    
    @staticmethod
    def _serialize_object(obj: Any) -> Dict[str, Any]:
        """
        序列化对象为字典
        
        Args:
            obj: 要序列化的对象
            
        Returns:
            序列化后的字典
        """
        result = {}
        
        # 获取对象的所有属性
        for key in dir(obj):
            if not key.startswith('_') and not callable(getattr(obj, key)):
                try:
                    value = getattr(obj, key)
                    result[key] = ResponseHelper._serialize_data(value)
                except Exception:
                    # 忽略无法序列化的属性
                    continue
        
        return result


class FinancialResponseHelper(ResponseHelper):
    """财务模块专用响应助手"""
    
    @staticmethod
    def voucher_response(voucher_id: int, voucher_number: str, 
                        message: str = "操作成功") -> dict:
        """
        凭证操作响应格式
        
        Args:
            voucher_id: 凭证ID
            voucher_number: 凭证号
            message: 响应消息
            
        Returns:
            JSON响应字典
        """
        return ResponseHelper.success_response(
            message=message,
            voucher_id=voucher_id,
            voucher_number=voucher_number
        )
    
    @staticmethod
    def balance_validation_response(is_balanced: bool, total_debit: float, 
                                  total_credit: float) -> dict:
        """
        借贷平衡验证响应格式
        
        Args:
            is_balanced: 是否平衡
            total_debit: 借方总额
            total_credit: 贷方总额
            
        Returns:
            JSON响应字典
        """
        if is_balanced:
            return ResponseHelper.success_response(
                message="借贷平衡验证通过",
                balance_info={
                    'is_balanced': True,
                    'total_debit': total_debit,
                    'total_credit': total_credit,
                    'difference': 0
                }
            )
        else:
            difference = abs(total_debit - total_credit)
            return ResponseHelper.error_response(
                message=f"借贷不平衡，差额: {difference}",
                code=400,
                details={
                    'balance_info': {
                        'is_balanced': False,
                        'total_debit': total_debit,
                        'total_credit': total_credit,
                        'difference': difference
                    }
                }
            )
    
    @staticmethod
    def financial_report_response(report_data: Dict[str, Any], 
                                report_type: str, period: str) -> dict:
        """
        财务报表响应格式
        
        Args:
            report_data: 报表数据
            report_type: 报表类型
            period: 报表期间
            
        Returns:
            JSON响应字典
        """
        return ResponseHelper.success_response(
            message="报表生成成功",
            data=report_data,
            report_info={
                'type': report_type,
                'period': period,
                'generated_at': datetime.now().isoformat()
            }
        )
    
    @staticmethod
    def payment_response(payment_id: int, payment_number: str, 
                        amount: float, message: str = "付款成功") -> dict:
        """
        付款操作响应格式
        
        Args:
            payment_id: 付款记录ID
            payment_number: 付款单号
            amount: 付款金额
            message: 响应消息
            
        Returns:
            JSON响应字典
        """
        return ResponseHelper.success_response(
            message=message,
            payment_id=payment_id,
            payment_number=payment_number,
            amount=amount
        )


class APIResponseBuilder:
    """API响应构建器"""
    
    def __init__(self):
        self.response_data = {
            'success': True,
            'message': '',
            'timestamp': datetime.now().isoformat()
        }
    
    def set_success(self, success: bool) -> 'APIResponseBuilder':
        """设置成功状态"""
        self.response_data['success'] = success
        return self
    
    def set_message(self, message: str) -> 'APIResponseBuilder':
        """设置响应消息"""
        self.response_data['message'] = message
        return self
    
    def set_data(self, data: Any) -> 'APIResponseBuilder':
        """设置响应数据"""
        self.response_data['data'] = ResponseHelper._serialize_data(data)
        return self
    
    def set_error_code(self, code: int) -> 'APIResponseBuilder':
        """设置错误代码"""
        self.response_data['error_code'] = code
        return self
    
    def set_details(self, details: Dict[str, Any]) -> 'APIResponseBuilder':
        """设置详细信息"""
        self.response_data['details'] = details
        return self
    
    def add_field(self, key: str, value: Any) -> 'APIResponseBuilder':
        """添加自定义字段"""
        self.response_data[key] = ResponseHelper._serialize_data(value)
        return self
    
    def build(self, status_code: int = 200) -> tuple:
        """构建最终响应"""
        if status_code == 200:
            return jsonify(self.response_data)
        else:
            return jsonify(self.response_data), status_code


# 便捷函数
def success(message: str, data: Any = None, **kwargs) -> dict:
    """快捷成功响应"""
    return ResponseHelper.success_response(message, data, **kwargs)


def error(message: str, code: int = 400, details: Dict[str, Any] = None, **kwargs) -> tuple:
    """快捷错误响应"""
    return ResponseHelper.error_response(message, code, details, **kwargs)


def paginated(data: List[Any], page: int, per_page: int, total: int, message: str = "查询成功") -> dict:
    """快捷分页响应"""
    return ResponseHelper.paginated_response(data, page, per_page, total, message)


def flash_success(message: str, endpoint: str = None, **kwargs) -> Any:
    """快捷成功Flash"""
    return ResponseHelper.flash_and_redirect(message, 'success', endpoint, **kwargs)


def flash_error(message: str, endpoint: str = None, **kwargs) -> Any:
    """快捷错误Flash"""
    return ResponseHelper.flash_and_redirect(message, 'danger', endpoint, **kwargs)


def flash_warning(message: str, endpoint: str = None, **kwargs) -> Any:
    """快捷警告Flash"""
    return ResponseHelper.flash_and_redirect(message, 'warning', endpoint, **kwargs)


def flash_info(message: str, endpoint: str = None, **kwargs) -> Any:
    """快捷信息Flash"""
    return ResponseHelper.flash_and_redirect(message, 'info', endpoint, **kwargs)
