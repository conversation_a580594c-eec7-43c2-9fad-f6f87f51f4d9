#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧食堂视频录制工具 - 图形界面版本
现代化的用户界面，支持脚本选择、环境检测、进度显示
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import subprocess
import sys
import os
import glob
import json
from pathlib import Path
import queue
import time

class VideoRecorderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("智慧食堂视频录制工具 v1.0")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        
        # 设置现代化样式
        self.setup_styles()
        
        # 创建主界面
        self.create_widgets()
        
        # 初始化变量
        self.script_vars = {}
        self.log_queue = queue.Queue()
        self.is_running = False
        
        # 加载脚本列表
        self.load_scripts()
        
        # 检查环境
        self.check_environment()
        
        # 启动日志更新线程
        self.start_log_updater()

    def setup_styles(self):
        """设置现代化样式"""
        style = ttk.Style()
        
        # 配置样式
        style.configure('Title.TLabel', font=('Microsoft YaHei UI', 16, 'bold'))
        style.configure('Heading.TLabel', font=('Microsoft YaHei UI', 12, 'bold'))
        style.configure('Status.TLabel', font=('Microsoft YaHei UI', 10))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Warning.TLabel', foreground='orange')

    def create_widgets(self):
        """创建主界面组件"""
        # 主容器
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="智慧食堂视频录制工具", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 左侧面板 - 环境检测和脚本选择
        left_frame = ttk.Frame(main_frame)
        left_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        left_frame.columnconfigure(0, weight=1)
        
        # 环境检测区域
        self.create_environment_section(left_frame)
        
        # 脚本选择区域
        self.create_script_selection_section(left_frame)
        
        # 控制按钮区域
        self.create_control_section(left_frame)
        
        # 右侧面板 - 日志输出
        right_frame = ttk.Frame(main_frame)
        right_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(1, weight=1)
        
        # 日志区域
        self.create_log_section(right_frame)
        
        # 进度条
        self.progress_frame = ttk.Frame(main_frame)
        self.progress_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        self.progress_frame.columnconfigure(0, weight=1)
        
        self.progress_var = tk.StringVar(value="就绪")
        self.progress_label = ttk.Label(self.progress_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=0, column=0, sticky=tk.W)
        
        self.progress_bar = ttk.Progressbar(self.progress_frame, mode='indeterminate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))

    def create_environment_section(self, parent):
        """创建环境检测区域"""
        env_frame = ttk.LabelFrame(parent, text="环境检测", padding="10")
        env_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        env_frame.columnconfigure(1, weight=1)
        
        # 环境状态显示
        self.env_status = {}
        dependencies = [
            ("Python", "python --version"),
            ("edge-tts", "pip show edge-tts"),
            ("playwright", "pip show playwright"),
            ("pydub", "pip show pydub"),
            ("ffmpeg", "ffmpeg -version")
        ]
        
        for i, (name, cmd) in enumerate(dependencies):
            label = ttk.Label(env_frame, text=f"{name}:")
            label.grid(row=i, column=0, sticky=tk.W, pady=2)
            
            status_label = ttk.Label(env_frame, text="检测中...", style='Status.TLabel')
            status_label.grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=2)
            
            self.env_status[name] = status_label
        
        # 刷新按钮
        refresh_btn = ttk.Button(env_frame, text="重新检测", command=self.check_environment)
        refresh_btn.grid(row=len(dependencies), column=0, columnspan=2, pady=(10, 0))

    def create_script_selection_section(self, parent):
        """创建脚本选择区域"""
        script_frame = ttk.LabelFrame(parent, text="选择要录制的脚本", padding="10")
        script_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        script_frame.columnconfigure(0, weight=1)
        script_frame.rowconfigure(1, weight=1)
        
        # 全选/取消全选
        select_frame = ttk.Frame(script_frame)
        select_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.select_all_var = tk.BooleanVar()
        select_all_cb = ttk.Checkbutton(select_frame, text="全选", variable=self.select_all_var, 
                                       command=self.toggle_all_scripts)
        select_all_cb.grid(row=0, column=0, sticky=tk.W)
        
        # 脚本列表
        self.script_listbox_frame = ttk.Frame(script_frame)
        self.script_listbox_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.script_listbox_frame.columnconfigure(0, weight=1)
        self.script_listbox_frame.rowconfigure(0, weight=1)

    def create_control_section(self, parent):
        """创建控制按钮区域"""
        control_frame = ttk.Frame(parent)
        control_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        control_frame.columnconfigure(0, weight=1)
        control_frame.columnconfigure(1, weight=1)
        
        self.start_btn = ttk.Button(control_frame, text="开始录制", command=self.start_recording)
        self.start_btn.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        self.stop_btn = ttk.Button(control_frame, text="停止录制", command=self.stop_recording, state='disabled')
        self.stop_btn.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 0))

        # 输出目录按钮
        output_btn = ttk.Button(control_frame, text="打开输出目录", command=self.open_output_dir)
        output_btn.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=(0, 5), pady=(10, 0))

        # 预览脚本按钮
        preview_btn = ttk.Button(control_frame, text="预览选中脚本", command=self.preview_scripts)
        preview_btn.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(5, 0), pady=(10, 0))

        # 脚本加工按钮
        process_btn = ttk.Button(control_frame, text="智能加工脚本", command=self.process_scripts)
        process_btn.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

    def create_log_section(self, parent):
        """创建日志输出区域"""
        log_frame = ttk.LabelFrame(parent, text="输出日志", padding="10")
        log_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=20, width=50)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清空日志按钮
        clear_btn = ttk.Button(log_frame, text="清空日志", command=self.clear_log)
        clear_btn.grid(row=1, column=0, pady=(10, 0))

    def load_scripts(self):
        """加载可用的脚本"""
        try:
            script_files = glob.glob("video-scripts/*.md")
            script_files = [f for f in script_files if not f.endswith("README.md")]
            
            # 清空现有的复选框
            for widget in self.script_listbox_frame.winfo_children():
                widget.destroy()
            
            self.script_vars.clear()
            
            if not script_files:
                no_scripts_label = ttk.Label(self.script_listbox_frame, text="未找到脚本文件")
                no_scripts_label.grid(row=0, column=0, pady=20)
                return
            
            # 创建滚动区域
            canvas = tk.Canvas(self.script_listbox_frame)
            scrollbar = ttk.Scrollbar(self.script_listbox_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = ttk.Frame(canvas)
            
            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )
            
            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)
            
            # 添加脚本复选框
            for i, script_file in enumerate(sorted(script_files)):
                script_name = os.path.basename(script_file).replace('.md', '')
                var = tk.BooleanVar()
                self.script_vars[script_name] = var
                
                cb = ttk.Checkbutton(scrollable_frame, text=script_name, variable=var)
                cb.grid(row=i, column=0, sticky=tk.W, pady=2, padx=5)
            
            canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
            scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
            
            self.script_listbox_frame.columnconfigure(0, weight=1)
            self.script_listbox_frame.rowconfigure(0, weight=1)
            
            self.log(f"加载了 {len(script_files)} 个脚本文件")
            
        except Exception as e:
            self.log(f"加载脚本失败: {e}", "ERROR")

    def check_environment(self):
        """检查运行环境"""
        def check_dependency(name, command):
            try:
                result = subprocess.run(command.split(), capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    self.env_status[name].config(text="✓ 已安装", style='Success.TLabel')
                    return True
                else:
                    self.env_status[name].config(text="✗ 未安装", style='Error.TLabel')
                    return False
            except (subprocess.TimeoutExpired, FileNotFoundError):
                self.env_status[name].config(text="✗ 未找到", style='Error.TLabel')
                return False
            except Exception as e:
                self.env_status[name].config(text=f"✗ 错误: {str(e)[:20]}", style='Error.TLabel')
                return False
        
        def check_all():
            dependencies = [
                ("Python", "python --version"),
                ("edge-tts", "pip show edge-tts"),
                ("playwright", "pip show playwright"),
                ("pydub", "pip show pydub"),
                ("ffmpeg", "ffmpeg -version")
            ]
            
            missing = []
            for name, cmd in dependencies:
                if not check_dependency(name, cmd):
                    missing.append(name)
            
            if missing:
                self.show_installation_guide(missing)
            else:
                self.log("✓ 所有依赖项检查通过", "SUCCESS")
        
        # 在后台线程中检查
        threading.Thread(target=check_all, daemon=True).start()

    def show_installation_guide(self, missing_deps):
        """显示安装指南"""
        guide = {
            "edge-tts": "pip install edge-tts",
            "playwright": "pip install playwright && playwright install",
            "pydub": "pip install pydub",
            "ffmpeg": "请访问 https://ffmpeg.org/download.html 下载并安装 FFmpeg"
        }
        
        message = "检测到缺少以下依赖项，请按照提示安装：\n\n"
        for dep in missing_deps:
            if dep in guide:
                message += f"• {dep}: {guide[dep]}\n"
        
        messagebox.showwarning("缺少依赖项", message)
        self.log("⚠ 检测到缺少依赖项，请查看弹出的安装指南", "WARNING")

    def toggle_all_scripts(self):
        """切换全选状态"""
        select_all = self.select_all_var.get()
        for var in self.script_vars.values():
            var.set(select_all)

    def start_recording(self):
        """开始录制"""
        selected_scripts = [name for name, var in self.script_vars.items() if var.get()]
        
        if not selected_scripts:
            messagebox.showwarning("提示", "请至少选择一个脚本进行录制")
            return
        
        self.is_running = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.progress_bar.start()
        self.progress_var.set(f"正在录制 {len(selected_scripts)} 个脚本...")
        
        # 在后台线程中执行录制
        threading.Thread(target=self.run_recording, args=(selected_scripts,), daemon=True).start()

    def run_recording(self, selected_scripts):
        """执行录制任务 - 智能按需处理"""
        try:
            self.log(f"🚀 开始智能录制 {len(selected_scripts)} 个脚本", "INFO")

            # 导入主程序模块
            import main

            # 创建脚本文件列表
            script_files = []
            for script_name in selected_scripts:
                script_path = f"video-scripts/{script_name}.md"
                if os.path.exists(script_path):
                    script_files.append(script_path)
                else:
                    self.log(f"⚠️ 脚本文件不存在: {script_path}", "WARNING")

            if not script_files:
                self.log("❌ 没有找到有效的脚本文件", "ERROR")
                return

            # 逐个智能处理脚本
            for i, script_file in enumerate(script_files, 1):
                if not self.is_running:
                    self.log("⏹️ 用户停止录制", "WARNING")
                    break

                script_name = os.path.basename(script_file).replace('.md', '')
                self.log(f"📝 处理脚本: {script_name} ({i}/{len(script_files)})", "INFO")

                try:
                    # 创建新的事件循环
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    # 智能处理单个脚本
                    loop.run_until_complete(main.process_one_md(script_file))
                    loop.close()

                    self.log(f"✅ 完成: {script_name}", "SUCCESS")

                except Exception as e:
                    self.log(f"❌ 处理 {script_name} 时出错: {e}", "ERROR")
                    # 继续处理下一个脚本，不中断整个流程
                    continue

            # 最终状态报告
            if self.is_running:
                self.log("🎉 所有脚本录制完成！", "SUCCESS")
                self.log("📁 输出文件保存在 output 目录中", "INFO")
                self.log("💡 提示：点击'打开输出目录'查看结果", "INFO")
            else:
                self.log("⏹️ 录制已停止", "WARNING")

        except Exception as e:
            self.log(f"❌ 录制过程中出错: {e}", "ERROR")
        finally:
            self.recording_finished()

    def stop_recording(self):
        """停止录制"""
        self.is_running = False
        self.log("正在停止录制...", "WARNING")

    def recording_finished(self):
        """录制完成后的清理工作"""
        self.root.after(0, self._recording_finished_ui)

    def _recording_finished_ui(self):
        """在主线程中更新UI"""
        self.is_running = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.progress_bar.stop()
        self.progress_var.set("就绪")

    def log(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_queue.put((log_entry, level))

    def start_log_updater(self):
        """启动日志更新器"""
        def update_log():
            try:
                while not self.log_queue.empty():
                    try:
                        log_entry, level = self.log_queue.get_nowait()
                        # 在主线程中更新UI
                        self.root.after(0, self._update_log_text, log_entry, level)
                    except queue.Empty:
                        break
            except Exception as e:
                print(f"日志更新错误: {e}")

            # 继续检查
            self.root.after(100, update_log)

        update_log()

    def _update_log_text(self, log_entry, level):
        """在主线程中更新日志文本"""
        # 根据日志级别设置颜色（可选功能）
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def open_output_dir(self):
        """打开输出目录"""
        output_dir = "output"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        try:
            if sys.platform.startswith("win"):
                os.startfile(output_dir)
            elif sys.platform.startswith("darwin"):
                subprocess.run(["open", output_dir])
            else:
                subprocess.run(["xdg-open", output_dir])
            self.log("📁 已打开输出目录", "INFO")
        except Exception as e:
            self.log(f"无法打开输出目录: {e}", "ERROR")

    def preview_scripts(self):
        """预览选中的脚本"""
        selected_scripts = [name for name, var in self.script_vars.items() if var.get()]

        if not selected_scripts:
            messagebox.showinfo("提示", "请先选择要预览的脚本")
            return

        # 导入主程序模块进行解析
        try:
            import main

            preview_text = f"📋 预览选中的 {len(selected_scripts)} 个脚本\n"
            preview_text += "=" * 60 + "\n\n"

            for i, script_name in enumerate(selected_scripts, 1):
                script_path = f"video-scripts/{script_name}.md"
                if os.path.exists(script_path):
                    steps = main.extract_steps_from_md(script_path)
                    preview_text += f"{i}. 📝 {script_name}\n"
                    preview_text += f"   步骤数量: {len(steps)}\n"

                    if steps:
                        preview_text += "   主要操作:\n"
                        for j, step in enumerate(steps[:3], 1):  # 只显示前3个步骤
                            operation = step.get('operation', step.get('narration', '')[:30] + '...')
                            preview_text += f"     {j}. {step['action']}({step['selector']}) - {operation}\n"

                        if len(steps) > 3:
                            preview_text += f"     ... 还有 {len(steps) - 3} 个步骤\n"
                    else:
                        preview_text += "   ⚠️ 未找到有效步骤\n"
                else:
                    preview_text += f"{i}. ❌ {script_name} (文件不存在)\n"

                preview_text += "\n"

            # 显示预览窗口
            preview_window = tk.Toplevel(self.root)
            preview_window.title("脚本预览")
            preview_window.geometry("800x600")

            text_widget = scrolledtext.ScrolledText(preview_window, wrap=tk.WORD)
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            text_widget.insert(tk.END, preview_text)
            text_widget.config(state=tk.DISABLED)

            self.log(f"📋 已预览 {len(selected_scripts)} 个脚本", "INFO")

        except Exception as e:
            self.log(f"预览脚本时出错: {e}", "ERROR")
            messagebox.showerror("错误", f"预览脚本时出错: {e}")

    def process_scripts(self):
        """智能加工脚本"""
        selected_scripts = [name for name, var in self.script_vars.items() if var.get()]

        if not selected_scripts:
            messagebox.showinfo("提示", "请先选择要加工的脚本")
            return

        # 确认操作
        result = messagebox.askyesno(
            "确认加工",
            f"将对选中的 {len(selected_scripts)} 个脚本进行智能加工，\n"
            "生成标准格式的操作步骤。\n\n"
            "原文件不会被修改，会生成新的加工版本。\n\n"
            "是否继续？"
        )

        if not result:
            return

        try:
            self.log(f"🔧 开始智能加工 {len(selected_scripts)} 个脚本", "INFO")
            self.log("💡 提示：当前版本的脚本加工功能已集成到主程序中", "INFO")

            processed_count = 0
            failed_count = 0

            for script_name in selected_scripts:
                script_path = f"video-scripts/{script_name}.md"

                if not os.path.exists(script_path):
                    self.log(f"❌ 脚本文件不存在: {script_name}", "ERROR")
                    failed_count += 1
                    continue

                try:
                    self.log(f"🔄 分析脚本: {script_name}", "INFO")

                    # 使用主程序的解析功能
                    import main
                    steps = main.extract_steps_from_md(script_path)

                    if not steps:
                        self.log(f"❌ 未找到有效步骤: {script_name}", "ERROR")
                        failed_count += 1
                        continue

                    # 显示分析结果
                    self.log(f"📊 分析结果: {script_name} - 解析到 {len(steps)} 个步骤", "INFO")

                    # 显示前几个步骤作为预览
                    self.log(f"💡 前3个步骤预览:", "INFO")
                    for i, step in enumerate(steps[:3], 1):
                        operation = step.get('operation', step.get('narration', ''))[:30] + '...'
                        self.log(f"   {i}. {step['action']}({step['selector']}) - {operation}", "INFO")

                    self.log(f"✅ 分析完成: {script_name}", "SUCCESS")
                    processed_count += 1

                except Exception as e:
                    self.log(f"❌ 分析失败: {script_name} - {e}", "ERROR")
                    failed_count += 1

            # 显示结果摘要
            self.log(f"🎉 脚本加工完成！", "SUCCESS")
            self.log(f"📊 成功: {processed_count} 个，失败: {failed_count} 个", "INFO")

            if processed_count > 0:
                self.log(f"💡 提示：脚本分析完成，系统已自动添加登录步骤", "INFO")
                self.log(f"🎯 建议：现在可以直接选择这些脚本进行录制", "INFO")
        except Exception as e:
            self.log(f"❌ 脚本加工过程中出错: {e}", "ERROR")
            messagebox.showerror("错误", f"脚本加工过程中出错: {e}")

def main():
    """主函数"""
    root = tk.Tk()
    VideoRecorderGUI(root)

    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap('icon.ico')
    except:
        pass

    # 启动主循环
    root.mainloop()

if __name__ == "__main__":
    main()
