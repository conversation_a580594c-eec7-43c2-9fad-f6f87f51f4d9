#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧食堂视频录制工具 - 图形界面版本
现代化的用户界面，支持脚本选择、环境检测、进度显示
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import subprocess
import sys
import os
import glob
import json
from pathlib import Path
import queue
import time

class VideoRecorderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("智慧食堂视频录制工具 v1.0")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        
        # 设置现代化样式
        self.setup_styles()
        
        # 创建主界面
        self.create_widgets()
        
        # 初始化变量
        self.script_vars = {}
        self.log_queue = queue.Queue()
        self.is_running = False
        
        # 加载脚本列表
        self.load_scripts()
        
        # 检查环境
        self.check_environment()
        
        # 启动日志更新线程
        self.start_log_updater()

    def setup_styles(self):
        """设置现代化样式"""
        style = ttk.Style()
        
        # 配置样式
        style.configure('Title.TLabel', font=('Microsoft YaHei UI', 16, 'bold'))
        style.configure('Heading.TLabel', font=('Microsoft YaHei UI', 12, 'bold'))
        style.configure('Status.TLabel', font=('Microsoft YaHei UI', 10))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Warning.TLabel', foreground='orange')

    def create_widgets(self):
        """创建主界面组件"""
        # 主容器
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="智慧食堂视频录制工具", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 左侧面板 - 环境检测和脚本选择
        left_frame = ttk.Frame(main_frame)
        left_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        left_frame.columnconfigure(0, weight=1)
        
        # 环境检测区域
        self.create_environment_section(left_frame)
        
        # 脚本选择区域
        self.create_script_selection_section(left_frame)
        
        # 控制按钮区域
        self.create_control_section(left_frame)
        
        # 右侧面板 - 日志输出
        right_frame = ttk.Frame(main_frame)
        right_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(1, weight=1)
        
        # 日志区域
        self.create_log_section(right_frame)
        
        # 进度条
        self.progress_frame = ttk.Frame(main_frame)
        self.progress_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        self.progress_frame.columnconfigure(0, weight=1)
        
        self.progress_var = tk.StringVar(value="就绪")
        self.progress_label = ttk.Label(self.progress_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=0, column=0, sticky=tk.W)
        
        self.progress_bar = ttk.Progressbar(self.progress_frame, mode='indeterminate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))

    def create_environment_section(self, parent):
        """创建环境检测区域"""
        env_frame = ttk.LabelFrame(parent, text="环境检测", padding="10")
        env_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        env_frame.columnconfigure(1, weight=1)
        
        # 环境状态显示
        self.env_status = {}
        dependencies = [
            ("Python", "python --version"),
            ("edge-tts", "pip show edge-tts"),
            ("playwright", "pip show playwright"),
            ("pydub", "pip show pydub"),
            ("ffmpeg", "ffmpeg -version")
        ]
        
        for i, (name, cmd) in enumerate(dependencies):
            label = ttk.Label(env_frame, text=f"{name}:")
            label.grid(row=i, column=0, sticky=tk.W, pady=2)
            
            status_label = ttk.Label(env_frame, text="检测中...", style='Status.TLabel')
            status_label.grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=2)
            
            self.env_status[name] = status_label
        
        # 刷新按钮
        refresh_btn = ttk.Button(env_frame, text="重新检测", command=self.check_environment)
        refresh_btn.grid(row=len(dependencies), column=0, columnspan=2, pady=(10, 0))

    def create_script_selection_section(self, parent):
        """创建脚本选择区域"""
        script_frame = ttk.LabelFrame(parent, text="选择要录制的脚本", padding="10")
        script_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        script_frame.columnconfigure(0, weight=1)
        script_frame.rowconfigure(1, weight=1)
        
        # 全选/取消全选
        select_frame = ttk.Frame(script_frame)
        select_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.select_all_var = tk.BooleanVar()
        select_all_cb = ttk.Checkbutton(select_frame, text="全选", variable=self.select_all_var, 
                                       command=self.toggle_all_scripts)
        select_all_cb.grid(row=0, column=0, sticky=tk.W)
        
        # 脚本列表
        self.script_listbox_frame = ttk.Frame(script_frame)
        self.script_listbox_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.script_listbox_frame.columnconfigure(0, weight=1)
        self.script_listbox_frame.rowconfigure(0, weight=1)

    def create_control_section(self, parent):
        """创建控制按钮区域"""
        control_frame = ttk.Frame(parent)
        control_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        control_frame.columnconfigure(0, weight=1)
        control_frame.columnconfigure(1, weight=1)
        
        self.start_btn = ttk.Button(control_frame, text="智能录制(含预检测)", command=self.start_recording)
        self.start_btn.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        self.stop_btn = ttk.Button(control_frame, text="停止录制", command=self.stop_recording, state='disabled')
        self.stop_btn.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 0))

        # 输出目录按钮
        output_btn = ttk.Button(control_frame, text="打开输出目录", command=self.open_output_dir)
        output_btn.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=(0, 5), pady=(10, 0))

        # 预览脚本按钮
        preview_btn = ttk.Button(control_frame, text="预览选中脚本", command=self.preview_scripts)
        preview_btn.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(5, 0), pady=(10, 0))

        # 脚本加工按钮
        process_btn = ttk.Button(control_frame, text="智能加工脚本", command=self.process_scripts)
        process_btn.grid(row=2, column=0, sticky=(tk.W, tk.E), padx=(0, 5), pady=(10, 0))

        # 脚本测试按钮
        test_btn = ttk.Button(control_frame, text="测试并修复脚本", command=self.test_scripts)
        test_btn.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(5, 0), pady=(10, 0))

        # 智能修复按钮
        intelligent_fix_btn = ttk.Button(control_frame, text="智能修复(基于项目结构)", command=self.intelligent_fix_scripts)
        intelligent_fix_btn.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))

    def create_log_section(self, parent):
        """创建日志输出区域"""
        log_frame = ttk.LabelFrame(parent, text="输出日志", padding="10")
        log_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=20, width=50)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清空日志按钮
        clear_btn = ttk.Button(log_frame, text="清空日志", command=self.clear_log)
        clear_btn.grid(row=1, column=0, pady=(10, 0))

    def load_scripts(self):
        """加载可用的脚本"""
        try:
            script_files = glob.glob("video-scripts/*.md")
            script_files = [f for f in script_files if not f.endswith("README.md")]
            
            # 清空现有的复选框
            for widget in self.script_listbox_frame.winfo_children():
                widget.destroy()
            
            self.script_vars.clear()
            
            if not script_files:
                no_scripts_label = ttk.Label(self.script_listbox_frame, text="未找到脚本文件")
                no_scripts_label.grid(row=0, column=0, pady=20)
                return
            
            # 创建滚动区域
            canvas = tk.Canvas(self.script_listbox_frame)
            scrollbar = ttk.Scrollbar(self.script_listbox_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = ttk.Frame(canvas)
            
            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )
            
            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)
            
            # 添加脚本复选框
            for i, script_file in enumerate(sorted(script_files)):
                script_name = os.path.basename(script_file).replace('.md', '')
                var = tk.BooleanVar()
                self.script_vars[script_name] = var
                
                cb = ttk.Checkbutton(scrollable_frame, text=script_name, variable=var)
                cb.grid(row=i, column=0, sticky=tk.W, pady=2, padx=5)
            
            canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
            scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
            
            self.script_listbox_frame.columnconfigure(0, weight=1)
            self.script_listbox_frame.rowconfigure(0, weight=1)
            
            self.log(f"加载了 {len(script_files)} 个脚本文件")
            
        except Exception as e:
            self.log(f"加载脚本失败: {e}", "ERROR")

    def check_environment(self):
        """检查运行环境"""
        def check_dependency(name, command):
            try:
                result = subprocess.run(command.split(), capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    self.env_status[name].config(text="✓ 已安装", style='Success.TLabel')
                    return True
                else:
                    self.env_status[name].config(text="✗ 未安装", style='Error.TLabel')
                    return False
            except (subprocess.TimeoutExpired, FileNotFoundError):
                self.env_status[name].config(text="✗ 未找到", style='Error.TLabel')
                return False
            except Exception as e:
                self.env_status[name].config(text=f"✗ 错误: {str(e)[:20]}", style='Error.TLabel')
                return False
        
        def check_all():
            dependencies = [
                ("Python", "python --version"),
                ("edge-tts", "pip show edge-tts"),
                ("playwright", "pip show playwright"),
                ("pydub", "pip show pydub"),
                ("ffmpeg", "ffmpeg -version")
            ]
            
            missing = []
            for name, cmd in dependencies:
                if not check_dependency(name, cmd):
                    missing.append(name)
            
            if missing:
                self.show_installation_guide(missing)
            else:
                self.log("✓ 所有依赖项检查通过", "SUCCESS")
        
        # 在后台线程中检查
        threading.Thread(target=check_all, daemon=True).start()

    def show_installation_guide(self, missing_deps):
        """显示安装指南"""
        guide = {
            "edge-tts": "pip install edge-tts",
            "playwright": "pip install playwright && playwright install",
            "pydub": "pip install pydub",
            "ffmpeg": "请访问 https://ffmpeg.org/download.html 下载并安装 FFmpeg"
        }
        
        message = "检测到缺少以下依赖项，请按照提示安装：\n\n"
        for dep in missing_deps:
            if dep in guide:
                message += f"• {dep}: {guide[dep]}\n"
        
        messagebox.showwarning("缺少依赖项", message)
        self.log("⚠ 检测到缺少依赖项，请查看弹出的安装指南", "WARNING")

    def toggle_all_scripts(self):
        """切换全选状态"""
        select_all = self.select_all_var.get()
        for var in self.script_vars.values():
            var.set(select_all)

    def start_recording(self):
        """开始录制（包含自动预检测）"""
        selected_scripts = [name for name, var in self.script_vars.items() if var.get()]

        if not selected_scripts:
            messagebox.showwarning("提示", "请至少选择一个脚本进行录制")
            return

        # 确认开始录制（包含预检测说明）
        result = messagebox.askyesno(
            "确认录制",
            f"将录制选中的 {len(selected_scripts)} 个脚本。\n\n"
            "录制前将自动执行以下步骤：\n"
            "1. 🧪 脚本预检测和自动修复\n"
            "2. 📊 确保100%操作成功率\n"
            "3. 🎬 开始高质量录制\n\n"
            "整个过程可能需要较长时间，是否继续？"
        )
        if not result:
            return

        self.is_running = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.progress_bar.start()
        self.progress_var.set(f"正在预检测和录制 {len(selected_scripts)} 个脚本...")

        # 在后台线程中执行录制（包含预检测）
        threading.Thread(target=self.run_recording_with_pretest, args=(selected_scripts,), daemon=True).start()

    def run_recording_with_pretest(self, selected_scripts):
        """执行录制任务 - 包含自动预检测和修复"""
        try:
            self.log(f"🚀 开始智能录制 {len(selected_scripts)} 个脚本", "INFO")
            self.log("📋 录制流程：预检测 → 自动修复 → 高质量录制", "INFO")

            # 导入必要模块
            import main
            import asyncio
            from script_tester import test_script_comprehensive

            # 创建脚本文件列表
            script_files = []
            for script_name in selected_scripts:
                script_path = f"video-scripts/{script_name}.md"
                if os.path.exists(script_path):
                    script_files.append(script_path)
                else:
                    self.log(f"⚠️ 脚本文件不存在: {script_path}", "WARNING")

            if not script_files:
                self.log("❌ 没有找到有效的脚本文件", "ERROR")
                return

            # 第一阶段：预检测和自动修复
            self.log("🧪 第一阶段：脚本预检测和自动修复", "INFO")
            self.log("=" * 50, "INFO")

            pretest_success_count = 0
            pretest_failed_count = 0

            for i, script_file in enumerate(script_files, 1):
                if not self.is_running:
                    self.log("⏹️ 用户停止录制", "WARNING")
                    return

                script_name = os.path.basename(script_file).replace('.md', '')
                self.log(f"🔍 预检测脚本: {script_name} ({i}/{len(script_files)})", "INFO")

                try:
                    # 解析脚本生成actions
                    steps = main.extract_steps_from_md(script_file)
                    if not steps:
                        self.log(f"❌ 脚本解析失败: {script_name}", "ERROR")
                        pretest_failed_count += 1
                        continue

                    # 生成actions文件
                    actions_json = main.generate_actions_json(steps, script_name)
                    self.log(f"📄 生成测试配置: {os.path.basename(actions_json)}", "INFO")

                    # 执行全面测试
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    test_results, fixed_file = loop.run_until_complete(
                        test_script_comprehensive(actions_json)
                    )
                    loop.close()

                    # 评估测试结果
                    success_rate = test_results['success_rate']
                    self.log(f"📊 {script_name} 预检测结果: {success_rate:.1f}% 成功率",
                            "SUCCESS" if success_rate >= 90 else "WARNING")

                    if success_rate >= 80:
                        pretest_success_count += 1
                        if test_results['fixed_steps'] > 0:
                            self.log(f"🔧 已生成修复版本: {os.path.basename(fixed_file)}", "INFO")
                    else:
                        pretest_failed_count += 1
                        self.log(f"❌ {script_name} 预检测失败，成功率过低", "ERROR")

                except Exception as e:
                    self.log(f"❌ 预检测 {script_name} 时出错: {e}", "ERROR")
                    pretest_failed_count += 1

            # 预检测结果摘要
            self.log("=" * 50, "INFO")
            self.log(f"🧪 预检测完成: 成功 {pretest_success_count} 个，失败 {pretest_failed_count} 个", "INFO")

            if pretest_failed_count > 0:
                self.log(f"⚠️ 有 {pretest_failed_count} 个脚本预检测失败，将跳过录制", "WARNING")

            if pretest_success_count == 0:
                self.log("❌ 所有脚本预检测失败，停止录制", "ERROR")
                return

            # 第二阶段：高质量录制
            self.log("🎬 第二阶段：开始高质量录制", "INFO")
            self.log("=" * 50, "INFO")

            recording_success_count = 0
            recording_failed_count = 0

            for i, script_file in enumerate(script_files, 1):
                if not self.is_running:
                    self.log("⏹️ 用户停止录制", "WARNING")
                    break

                script_name = os.path.basename(script_file).replace('.md', '')
                base_name = script_name

                # 检查是否有修复版本
                fixed_actions = f"output/{base_name}_fixed.json"
                original_actions = f"output/{base_name}_actions.json"

                # 优先使用修复版本
                if os.path.exists(fixed_actions):
                    actions_file = fixed_actions
                    self.log(f"🔧 使用修复版本录制: {script_name}", "INFO")
                elif os.path.exists(original_actions):
                    actions_file = original_actions
                    self.log(f"📄 使用原始版本录制: {script_name}", "INFO")
                else:
                    self.log(f"❌ 未找到配置文件: {script_name}", "ERROR")
                    recording_failed_count += 1
                    continue

                try:
                    # 执行录制
                    mp4_path = os.path.join("output", main.unique_name(base_name, 'mp4'))
                    self.log(f"🎥 开始录制: {script_name}", "INFO")

                    ret = os.system(f"python run_playwright.py {actions_file} {mp4_path}")

                    if ret == 0:
                        self.log(f"✅ 录制成功: {script_name}", "SUCCESS")
                        recording_success_count += 1
                    else:
                        self.log(f"❌ 录制失败: {script_name} (返回码: {ret})", "ERROR")
                        recording_failed_count += 1

                except Exception as e:
                    self.log(f"❌ 录制 {script_name} 时出错: {e}", "ERROR")
                    recording_failed_count += 1

            # 最终结果报告
            self.log("=" * 50, "INFO")
            self.log("🎉 录制流程完成！", "SUCCESS")
            self.log(f"📊 最终统计:", "INFO")
            self.log(f"   预检测成功: {pretest_success_count} 个", "INFO")
            self.log(f"   录制成功: {recording_success_count} 个", "SUCCESS")
            self.log(f"   录制失败: {recording_failed_count} 个", "ERROR" if recording_failed_count > 0 else "INFO")

            if recording_success_count > 0:
                success_rate = (recording_success_count / len(script_files)) * 100
                self.log(f"🎯 总体成功率: {success_rate:.1f}%", "SUCCESS" if success_rate >= 80 else "WARNING")
                self.log("📁 输出文件保存在 output 目录中", "INFO")
                self.log("💡 提示：点击'打开输出目录'查看结果", "INFO")

        except Exception as e:
            self.log(f"❌ 录制过程中出错: {e}", "ERROR")
        finally:
            self.recording_finished()

    def run_recording(self, selected_scripts):
        """执行录制任务 - 兼容旧版本（已废弃，请使用 run_recording_with_pretest）"""
        self.log("⚠️ 使用旧版录制流程，建议使用新版本", "WARNING")
        self.run_recording_with_pretest(selected_scripts)

    def stop_recording(self):
        """停止录制"""
        self.is_running = False
        self.log("正在停止录制...", "WARNING")

    def recording_finished(self):
        """录制完成后的清理工作"""
        self.root.after(0, self._recording_finished_ui)

    def _recording_finished_ui(self):
        """在主线程中更新UI"""
        self.is_running = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.progress_bar.stop()
        self.progress_var.set("就绪")

    def log(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_queue.put((log_entry, level))

    def start_log_updater(self):
        """启动日志更新器"""
        def update_log():
            try:
                while not self.log_queue.empty():
                    try:
                        log_entry, level = self.log_queue.get_nowait()
                        # 在主线程中更新UI
                        self.root.after(0, self._update_log_text, log_entry, level)
                    except queue.Empty:
                        break
            except Exception as e:
                print(f"日志更新错误: {e}")

            # 继续检查
            self.root.after(100, update_log)

        update_log()

    def _update_log_text(self, log_entry, level):
        """在主线程中更新日志文本"""
        # 根据日志级别设置颜色（可选功能）
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def open_output_dir(self):
        """打开输出目录"""
        output_dir = "output"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        try:
            if sys.platform.startswith("win"):
                os.startfile(output_dir)
            elif sys.platform.startswith("darwin"):
                subprocess.run(["open", output_dir])
            else:
                subprocess.run(["xdg-open", output_dir])
            self.log("📁 已打开输出目录", "INFO")
        except Exception as e:
            self.log(f"无法打开输出目录: {e}", "ERROR")

    def preview_scripts(self):
        """预览选中的脚本"""
        selected_scripts = [name for name, var in self.script_vars.items() if var.get()]

        if not selected_scripts:
            messagebox.showinfo("提示", "请先选择要预览的脚本")
            return

        # 导入主程序模块进行解析
        try:
            import main

            preview_text = f"📋 预览选中的 {len(selected_scripts)} 个脚本\n"
            preview_text += "=" * 60 + "\n\n"

            for i, script_name in enumerate(selected_scripts, 1):
                script_path = f"video-scripts/{script_name}.md"
                if os.path.exists(script_path):
                    steps = main.extract_steps_from_md(script_path)
                    preview_text += f"{i}. 📝 {script_name}\n"
                    preview_text += f"   步骤数量: {len(steps)}\n"

                    if steps:
                        preview_text += "   主要操作:\n"
                        for j, step in enumerate(steps[:3], 1):  # 只显示前3个步骤
                            operation = step.get('operation', step.get('narration', '')[:30] + '...')
                            preview_text += f"     {j}. {step['action']}({step['selector']}) - {operation}\n"

                        if len(steps) > 3:
                            preview_text += f"     ... 还有 {len(steps) - 3} 个步骤\n"
                    else:
                        preview_text += "   ⚠️ 未找到有效步骤\n"
                else:
                    preview_text += f"{i}. ❌ {script_name} (文件不存在)\n"

                preview_text += "\n"

            # 显示预览窗口
            preview_window = tk.Toplevel(self.root)
            preview_window.title("脚本预览")
            preview_window.geometry("800x600")

            text_widget = scrolledtext.ScrolledText(preview_window, wrap=tk.WORD)
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            text_widget.insert(tk.END, preview_text)
            text_widget.config(state=tk.DISABLED)

            self.log(f"📋 已预览 {len(selected_scripts)} 个脚本", "INFO")

        except Exception as e:
            self.log(f"预览脚本时出错: {e}", "ERROR")
            messagebox.showerror("错误", f"预览脚本时出错: {e}")

    def process_scripts(self):
        """智能加工脚本"""
        selected_scripts = [name for name, var in self.script_vars.items() if var.get()]

        if not selected_scripts:
            messagebox.showinfo("提示", "请先选择要加工的脚本")
            return

        # 确认操作
        result = messagebox.askyesno(
            "确认加工",
            f"将对选中的 {len(selected_scripts)} 个脚本进行智能加工，\n"
            "生成标准格式的操作步骤。\n\n"
            "原文件不会被修改，会生成新的加工版本。\n\n"
            "是否继续？"
        )

        if not result:
            return

        try:
            self.log(f"🔧 开始智能加工 {len(selected_scripts)} 个脚本", "INFO")
            self.log("💡 提示：当前版本的脚本加工功能已集成到主程序中", "INFO")

            processed_count = 0
            failed_count = 0

            for script_name in selected_scripts:
                script_path = f"video-scripts/{script_name}.md"

                if not os.path.exists(script_path):
                    self.log(f"❌ 脚本文件不存在: {script_name}", "ERROR")
                    failed_count += 1
                    continue

                try:
                    self.log(f"🔄 分析脚本: {script_name}", "INFO")

                    # 使用主程序的解析功能
                    import main
                    steps = main.extract_steps_from_md(script_path)

                    if not steps:
                        self.log(f"❌ 未找到有效步骤: {script_name}", "ERROR")
                        failed_count += 1
                        continue

                    # 显示分析结果
                    self.log(f"📊 分析结果: {script_name} - 解析到 {len(steps)} 个步骤", "INFO")

                    # 显示前几个步骤作为预览
                    self.log(f"💡 前3个步骤预览:", "INFO")
                    for i, step in enumerate(steps[:3], 1):
                        operation = step.get('operation', step.get('narration', ''))[:30] + '...'
                        self.log(f"   {i}. {step['action']}({step['selector']}) - {operation}", "INFO")

                    self.log(f"✅ 分析完成: {script_name}", "SUCCESS")
                    processed_count += 1

                except Exception as e:
                    self.log(f"❌ 分析失败: {script_name} - {e}", "ERROR")
                    failed_count += 1

            # 显示结果摘要
            self.log(f"🎉 脚本加工完成！", "SUCCESS")
            self.log(f"📊 成功: {processed_count} 个，失败: {failed_count} 个", "INFO")

            if processed_count > 0:
                self.log(f"💡 提示：脚本分析完成，系统已自动添加登录步骤", "INFO")
                self.log(f"🎯 建议：现在可以直接选择这些脚本进行录制", "INFO")
        except Exception as e:
            self.log(f"❌ 脚本加工过程中出错: {e}", "ERROR")
            messagebox.showerror("错误", f"脚本加工过程中出错: {e}")

    def test_scripts(self):
        """测试并修复脚本"""
        selected_scripts = [name for name, var in self.script_vars.items() if var.get()]

        if not selected_scripts:
            messagebox.showinfo("提示", "请先选择要测试的脚本")
            return

        # 确认操作
        result = messagebox.askyesno(
            "确认测试",
            f"将对选中的 {len(selected_scripts)} 个脚本进行全面测试，\n"
            "包括页面访问、元素定位、操作可执行性等。\n"
            "系统会自动修复发现的问题。\n\n"
            "测试过程可能需要几分钟时间，是否继续？"
        )

        if not result:
            return

        # 在后台线程中执行测试
        threading.Thread(target=self._run_script_tests, args=(selected_scripts,), daemon=True).start()

    def _run_script_tests(self, selected_scripts):
        """在后台线程中运行脚本测试"""
        try:
            self.log(f"🧪 开始全面测试 {len(selected_scripts)} 个脚本", "INFO")
            self.log("💡 测试将验证每个操作步骤的可执行性", "INFO")

            import asyncio
            import main
            from script_tester import test_script_comprehensive

            total_success_rate = 0
            tested_count = 0

            for script_name in selected_scripts:
                if not self.is_running:
                    self.log("⏹️ 用户停止测试", "WARNING")
                    break

                try:
                    self.log(f"🔍 测试脚本: {script_name}", "INFO")

                    # 首先生成actions文件
                    script_path = f"video-scripts/{script_name}.md"
                    if not os.path.exists(script_path):
                        self.log(f"❌ 脚本文件不存在: {script_name}", "ERROR")
                        continue

                    # 解析脚本生成actions
                    steps = main.extract_steps_from_md(script_path)
                    if not steps:
                        self.log(f"❌ 脚本解析失败: {script_name}", "ERROR")
                        continue

                    # 生成actions文件
                    actions_json = main.generate_actions_json(steps, script_name)
                    self.log(f"📄 生成测试配置: {os.path.basename(actions_json)}", "INFO")

                    # 执行全面测试
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    test_results, fixed_file = loop.run_until_complete(
                        test_script_comprehensive(actions_json)
                    )
                    loop.close()

                    # 显示测试结果
                    success_rate = test_results['success_rate']
                    total_success_rate += success_rate
                    tested_count += 1

                    self.log(f"📊 {script_name} 测试结果:", "INFO")
                    self.log(f"   总步骤: {test_results['total_steps']}", "INFO")
                    self.log(f"   成功: {test_results['passed_steps']}", "SUCCESS")
                    self.log(f"   修复: {test_results['fixed_steps']}", "WARNING")
                    self.log(f"   失败: {test_results['failed_steps']}", "ERROR")
                    self.log(f"   成功率: {success_rate:.1f}%", "SUCCESS" if success_rate >= 90 else "WARNING")

                    if test_results['fixed_steps'] > 0:
                        self.log(f"🔧 已生成修复版本: {os.path.basename(fixed_file)}", "INFO")

                    # 显示失败步骤的详细信息
                    failed_steps = [r for r in test_results['step_results'] if r['status'] == 'failed']
                    if failed_steps:
                        self.log(f"❌ 失败步骤详情:", "ERROR")
                        for step in failed_steps[:3]:  # 只显示前3个失败步骤
                            self.log(f"   步骤{step['step']}: {step['error']}", "ERROR")

                except Exception as e:
                    self.log(f"❌ 测试 {script_name} 时出错: {e}", "ERROR")
                    continue

            # 显示总体结果
            if tested_count > 0:
                avg_success_rate = total_success_rate / tested_count
                self.log(f"🎉 测试完成！", "SUCCESS")
                self.log(f"📊 平均成功率: {avg_success_rate:.1f}%", "SUCCESS" if avg_success_rate >= 90 else "WARNING")

                if avg_success_rate >= 95:
                    self.log(f"✅ 脚本质量优秀，可以直接录制", "SUCCESS")
                elif avg_success_rate >= 80:
                    self.log(f"⚠️ 脚本质量良好，建议使用修复版本录制", "WARNING")
                else:
                    self.log(f"❌ 脚本需要进一步优化", "ERROR")

                self.log(f"💡 提示：使用 *_fixed.json 文件进行录制可获得更好效果", "INFO")

        except Exception as e:
            self.log(f"❌ 脚本测试过程中出错: {e}", "ERROR")

    def intelligent_fix_scripts(self):
        """智能修复脚本(基于项目结构)"""
        selected_scripts = [name for name, var in self.script_vars.items() if var.get()]

        if not selected_scripts:
            messagebox.showinfo("提示", "请先选择要修复的脚本")
            return

        # 确认操作
        result = messagebox.askyesno(
            "确认智能修复",
            f"将对选中的 {len(selected_scripts)} 个脚本进行智能修复，\n"
            "基于APP项目结构分析，智能优化选择器和导航路径。\n"
            "这将生成新的修复版本文件。\n\n"
            "修复过程可能需要几分钟时间，是否继续？"
        )

        if not result:
            return

        # 在后台线程中执行智能修复
        threading.Thread(target=self._run_intelligent_fix, args=(selected_scripts,), daemon=True).start()

    def _run_intelligent_fix(self, selected_scripts):
        """在后台线程中运行智能修复"""
        try:
            self.log(f"🎯 开始智能修复 {len(selected_scripts)} 个脚本", "INFO")
            self.log("💡 基于APP项目结构分析，智能优化选择器", "INFO")

            import asyncio
            from intelligent_script_fixer import intelligent_fix_script

            fixed_count = 0

            for script_name in selected_scripts:
                actions_file = os.path.join("output", f"{script_name}_actions.json")
                if os.path.exists(actions_file):
                    self.log(f"🎯 正在智能修复: {script_name}", "INFO")

                    try:
                        # 运行智能修复
                        fixed_file = asyncio.run(intelligent_fix_script(actions_file))

                        if fixed_file:
                            fixed_count += 1
                            self.log(f"✅ 智能修复完成: {os.path.basename(fixed_file)}", "SUCCESS")
                        else:
                            self.log(f"⚠️ 智能修复未产生改进: {script_name}", "WARNING")

                    except Exception as e:
                        self.log(f"❌ 智能修复失败 {script_name}: {e}", "ERROR")
                else:
                    self.log(f"⚠️ 未找到脚本文件: {actions_file}", "WARNING")

            self.log(f"📊 智能修复完成: 成功修复 {fixed_count}/{len(selected_scripts)} 个脚本", "INFO")
            self.log("🎯 建议：使用修复后的脚本进行录制以获得更好效果", "INFO")

        except Exception as e:
            self.log(f"❌ 智能修复过程中出错: {e}", "ERROR")

def main():
    """主函数"""
    root = tk.Tk()
    VideoRecorderGUI(root)

    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap('icon.ico')
    except:
        pass

    # 启动主循环
    root.mainloop()

if __name__ == "__main__":
    main()
