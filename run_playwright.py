import sys
import json
import asyncio
from playwright.async_api import async_playwright
from pydub import AudioSegment
from pydub.playback import play
import threading
import os
import subprocess
import tempfile

def play_audio(audio_path):
    try:
        audio = AudioSegment.from_file(audio_path)
        play(audio)
    except Exception as e:
        print(f"音频播放失败: {e}")

def create_audio_timeline(actions):
    """创建音频时间轴，合成所有音频片段"""
    try:
        combined_audio = AudioSegment.empty()

        for step in actions:
            audio_path = f"output/{step['audio']}"
            if os.path.exists(audio_path):
                audio = AudioSegment.from_file(audio_path)
                combined_audio += audio
            else:
                print(f"警告：音频文件不存在: {audio_path}")
                # 添加1秒静音作为占位
                combined_audio += AudioSegment.silent(duration=1000)

        # 保存合成的音频
        temp_audio_path = "output/temp_combined_audio.wav"
        combined_audio.export(temp_audio_path, format="wav")
        return temp_audio_path

    except Exception as e:
        print(f"音频合成失败: {e}")
        return None

def merge_audio_video(video_path, audio_path, output_path):
    """使用 ffmpeg 合成音视频"""
    try:
        # 检查 ffmpeg 是否可用
        subprocess.run(["ffmpeg", "-version"], capture_output=True, check=True)

        # 高质量合成命令
        cmd = [
            "ffmpeg", "-y",  # -y 覆盖输出文件
            "-i", video_path,  # 输入视频
            "-i", audio_path,  # 输入音频
            "-c:v", "libx264",    # 视频编码：H.264 (更好的兼容性)
            "-preset", "medium",  # 编码预设：平衡质量和速度
            "-crf", "18",         # 质量因子：18 (高质量，范围0-51，越小越好)
            "-c:a", "aac",        # 音频编码：AAC
            "-b:a", "192k",       # 音频码率：192kbps (高质量)
            "-ar", "48000",       # 音频采样率：48kHz
            "-ac", "2",           # 音频声道：立体声
            "-movflags", "+faststart",  # 优化网络播放
            "-shortest",          # 以最短的流为准
            output_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"音视频合成成功: {output_path}")
            # 清理临时文件
            if os.path.exists(audio_path) and "temp_" in audio_path:
                os.remove(audio_path)
            return True
        else:
            print(f"ffmpeg 错误: {result.stderr}")
            return False

    except subprocess.CalledProcessError:
        print("错误：未找到 ffmpeg，请安装 ffmpeg 或将其添加到 PATH")
        return False
    except Exception as e:
        print(f"音视频合成失败: {e}")
        return False

async def run(actions_json, output_mp4):
    with open(actions_json, encoding="utf-8") as f:
        actions = json.load(f)

    # 创建合成音频
    print("正在合成音频时间轴...")
    combined_audio_path = create_audio_timeline(actions)

    async with async_playwright() as p:
        browser = await p.chromium.launch(
            headless=False,
            args=[
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--force-device-scale-factor=1'
            ]
        )

        # 高清视频录制配置
        context = await browser.new_context(
            record_video_dir="output",
            record_video_size={"width": 1920, "height": 1080},  # Full HD
            viewport={"width": 1920, "height": 1080}  # 浏览器窗口大小
        )
        page = await context.new_page()
        await page.goto("http://xiaoyuanst.com")
        for step in actions:
            print(f"步骤{step['step']}：{step['narration']} [{step['action']}] ...", flush=True)
            audio_path = f"output/{step['audio']}"
            t = threading.Thread(target=play_audio, args=(audio_path,))
            t.start()
            try:
                if step["action"] == "click":
                    await page.click(step["selector"])
                elif step["action"] == "fill":
                    await page.fill(step["selector"], step.get("value", ""))
                elif step["action"] == "wait":
                    await page.wait_for_timeout(int(step.get("value", 1000)))
                elif step["action"] == "goto":
                    url = step.get("value", "")
                    if not url.startswith("http"):
                        url = "http://" + url
                    await page.goto(url)
                else:
                    print(f"未知操作类型: {step['action']}, 跳过该步。")
            except Exception as e:
                print(f"执行步骤 {step['step']} 时出错: {e}")
            t.join()
        await context.close()
        await browser.close()

        import shutil, glob
        video_files = glob.glob("output/**/video.webm", recursive=True)
        if video_files:
            try:
                # 先移动原始视频文件
                temp_video_path = output_mp4.replace('.mp4', '_temp.webm')
                shutil.move(video_files[0], temp_video_path)
                print(f"原始视频录制完成: {temp_video_path}")

                # 如果有音频，进行音视频合成
                if combined_audio_path and os.path.exists(combined_audio_path):
                    print("正在合成音视频...")
                    if merge_audio_video(temp_video_path, combined_audio_path, output_mp4):
                        # 合成成功，删除临时视频文件
                        os.remove(temp_video_path)
                        print(f"带音频的视频生成完成: {output_mp4}")
                    else:
                        # 合成失败，保留原始视频
                        shutil.move(temp_video_path, output_mp4)
                        print(f"音视频合成失败，保留原始视频: {output_mp4}")
                else:
                    # 没有音频，直接使用原始视频
                    shutil.move(temp_video_path, output_mp4)
                    print(f"无音频，使用原始视频: {output_mp4}")

            except Exception as e:
                print(f"处理视频文件时出错: {e}")
        else:
            print("未找到录制视频文件！")

if __name__ == "__main__":
    asyncio.run(run(sys.argv[1], sys.argv[2]))