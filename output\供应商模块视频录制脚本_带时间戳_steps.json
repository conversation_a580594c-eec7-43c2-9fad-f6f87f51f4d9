[{"narration": "首先我们访问智慧食堂平台。我将使用游客账号登录，这样您也可以跟着一起操作体验。", "action": "goto", "selector": "", "operation": "访问 xiaoyuanst.com", "value": "http://xiaoyuanst.com"}, {"narration": "现在我们点击体验系统按钮，以游客身份进入系统。登录成功后，我们可以看到系统的主界面。", "action": "click", "selector": "text=体验系统", "operation": "点击\"体验系统\"进行游客登录"}, {"narration": "系统加载完成，我们现在可以看到智慧食堂的主界面，显示当前登录用户为guest_demo。接下来我们开始演示具体功能。", "action": "wait", "selector": "", "operation": "等待页面加载完成", "value": "3000"}, {"narration": "首先我们来看供应商分类管理。合理的分类有助于更好地组织和管理不同类型的供应商。", "action": "click", "selector": "text=供应商分类", "operation": "点击左侧导航 \"供应商管理\" → \"供应商分类\""}, {"narration": "我们先创建几个常用的供应商分类，比如蔬菜供应商、肉类供应商、调料供应商等。", "action": "click", "selector": "text=添加分类", "operation": "点击 \"添加分类\" 按钮"}, {"narration": "我们先创建几个常用的供应商分类，比如蔬菜供应商、肉类供应商、调料供应商等。", "action": "click", "selector": "text=提交", "operation": "点击 \"提交\" 按钮"}, {"narration": "系统支持对分类进行编辑和删除操作，但需要注意，如果分类下已有供应商，则不能删除。", "action": "click", "selector": "text=编辑", "operation": "点击某个分类的 \"编辑\" 按钮"}, {"narration": "接下来我们进入供应商管理的核心功能 - 供应商信息管理。这里可以查看所有合作的供应商信息。", "action": "click", "selector": "text=供应商列表", "operation": "点击左侧导航 \"供应商管理\" → \"供应商列表\""}, {"narration": "现在我们添加一个新的供应商。系统会自动建立供应商与学校的合作关系，并生成合同编号。", "action": "click", "selector": "text=添加供应商", "operation": "点击 \"添加供应商\" 按钮"}, {"narration": "现在我们添加一个新的供应商。系统会自动建立供应商与学校的合作关系，并生成合同编号。", "action": "click", "selector": "text=提交", "operation": "点击 \"提交\" 按钮"}, {"narration": "添加成功后，我们可以查看供应商的详细信息，包括基本资料、合作学校、产品信息等。", "action": "click", "selector": "text=查看", "operation": "点击刚添加供应商的 \"查看\" 按钮"}, {"narration": "如果供应商信息发生变化，我们可以随时进行编辑更新。", "action": "click", "selector": "text=编辑", "operation": "点击 \"编辑\" 按钮"}, {"narration": "供应商产品管理是整个模块的核心功能，这里管理着所有供应商提供的产品信息，包括审核、上架、下架等操作。", "action": "click", "selector": "text=产品管理", "operation": "点击左侧导航 \"供应商管理\" → \"产品管理\""}, {"narration": "我们先演示如何添加单个产品。每个产品都需要关联到具体的食材和供应商。", "action": "click", "selector": "text=添加产品", "operation": "点击 \"添加产品\" 按钮"}, {"narration": "我们先演示如何添加单个产品。每个产品都需要关联到具体的食材和供应商。", "action": "click", "selector": "text=绿源蔬菜合作社", "operation": "选择供应商: \"绿源蔬菜合作社\""}, {"narration": "我们先演示如何添加单个产品。每个产品都需要关联到具体的食材和供应商。", "action": "click", "selector": "text=白菜", "operation": "选择食材: \"白菜\""}, {"narration": "我们先演示如何添加单个产品。每个产品都需要关联到具体的食材和供应商。", "action": "click", "selector": "text=提交", "operation": "点击 \"提交\" 按钮"}, {"narration": "对于需要添加大量产品的情况，系统提供了批量添加功能，可以大大提高工作效率。", "action": "click", "selector": "text=批次管理", "operation": "点击 \"批次管理\" 按钮"}, {"narration": "对于需要添加大量产品的情况，系统提供了批量添加功能，可以大大提高工作效率。", "action": "click", "selector": "text=创建批次", "operation": "点击 \"创建批次\" 按钮"}, {"narration": "新添加的产品需要经过审核才能上架销售。我们来演示审核流程。", "action": "click", "selector": "text=审核通过", "operation": "点击 \"审核通过\" 按钮"}, {"narration": "新添加的产品需要经过审核才能上架销售。我们来演示审核流程。", "action": "click", "selector": "text=批量审核通过", "operation": "点击 \"批量审核通过\" 按钮"}, {"narration": "新添加的产品需要经过审核才能上架销售。我们来演示审核流程。", "action": "click", "selector": "text=拒绝", "operation": "点击 \"拒绝\" 按钮"}, {"narration": "审核通过的产品可以进行上架操作，上架后的产品可以在采购时选择。同时也支持下架操作。", "action": "click", "selector": "text=上架", "operation": "点击 \"上架\" 按钮"}, {"narration": "审核通过的产品可以进行上架操作，上架后的产品可以在采购时选择。同时也支持下架操作。", "action": "click", "selector": "text=批量上架", "operation": "点击 \"批量上架\" 按钮"}, {"narration": "审核通过的产品可以进行上架操作，上架后的产品可以在采购时选择。同时也支持下架操作。", "action": "click", "selector": "text=下架", "operation": "点击 \"下架\" 按钮"}, {"narration": "每个产品都有详细的信息页面，包括基本信息、规格参数、操作历史等。", "action": "click", "selector": "text=查看详情", "operation": "点击某个产品的 \"查看详情\" 按钮"}, {"narration": "对于需要详细规格说明的产品，可以添加规格参数。", "action": "click", "selector": "text=添加规格参数", "operation": "点击 \"添加规格参数\" 按钮"}]