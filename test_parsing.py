#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：验证操作解析修复效果
"""

import sys
import os
sys.path.append('.')

from main_fixed import guess_action_and_selector, extract_steps_from_md

def test_action_parsing():
    """测试操作解析功能"""
    print("🧪 测试操作解析功能")
    print("=" * 50)
    
    test_cases = [
        '访问 xiaoyuanst.com',
        '点击"体验系统"进行游客登录',
        '点击顶部导航栏"供应链"',
        '点击"供应商"进入供应商管理页面',
        '选择"肉类供应商"',
        '在关键词搜索框输入"蔬菜"',
        '输入供应商名称"张三"',
        '展示供应商列表表格',
        '指向页面头部各个功能区域'
    ]
    
    for i, case in enumerate(test_cases, 1):
        action, selector, *value = guess_action_and_selector(case)
        print(f"{i:2d}. 操作: {case}")
        print(f"    -> 动作: {action}")
        print(f"    -> 选择器: {selector}")
        if value and value[0]:
            print(f"    -> 值: {value[0]}")
        print()

def test_md_parsing():
    """测试 Markdown 文件解析"""
    print("📄 测试 Markdown 文件解析")
    print("=" * 50)
    
    # 测试一个具体的 MD 文件
    test_file = "video-scripts/01-supplier-management-demo.md"
    if os.path.exists(test_file):
        steps = extract_steps_from_md(test_file)
        print(f"文件: {test_file}")
        print(f"解析到 {len(steps)} 个步骤:")
        print()
        
        for i, step in enumerate(steps[:5], 1):  # 只显示前5个步骤
            print(f"步骤 {i}:")
            print(f"  操作: {step.get('operation', 'N/A')}")
            print(f"  动作: {step['action']}")
            print(f"  选择器: {step['selector']}")
            if step.get('value'):
                print(f"  值: {step['value']}")
            print(f"  解说词: {step['narration'][:50]}...")
            print()
        
        if len(steps) > 5:
            print(f"... 还有 {len(steps) - 5} 个步骤")
    else:
        print(f"测试文件不存在: {test_file}")

def compare_with_original():
    """对比原始解析结果"""
    print("🔄 对比原始解析结果")
    print("=" * 50)
    
    # 检查现有的 actions.json 文件
    import glob
    json_files = glob.glob("output/*_actions.json")
    
    if json_files:
        import json
        sample_file = json_files[0]
        print(f"检查文件: {sample_file}")
        
        with open(sample_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("原始解析结果中的问题:")
        for step in data[:3]:  # 检查前3个步骤
            selector = step.get('selector', '')
            if len(selector) < 10 and 'text=' in selector:
                print(f"  ❌ 步骤{step['step']}: 选择器过短 '{selector}'")
            else:
                print(f"  ✅ 步骤{step['step']}: 选择器正常 '{selector}'")
    else:
        print("未找到现有的 actions.json 文件")

def main():
    """主测试函数"""
    print("🔧 项目问题修复验证")
    print("=" * 60)
    print()
    
    test_action_parsing()
    print()
    test_md_parsing()
    print()
    compare_with_original()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成！")
    print("\n📋 修复总结:")
    print("1. ✅ 修复了选择器解析过短的问题")
    print("2. ✅ 改进了正则表达式匹配逻辑")
    print("3. ✅ 添加了调试信息输出")
    print("4. ✅ 移除了未使用的导入")
    print("\n🚀 使用修复版本:")
    print("   python main_fixed.py")

if __name__ == "__main__":
    main()
