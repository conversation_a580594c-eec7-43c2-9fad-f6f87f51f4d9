#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本预测试和自动修复模块
确保录制脚本100%可执行
"""

import asyncio
import json
import os
import re
from playwright.async_api import async_playwright
from typing import List, Dict, Tuple, Optional

class ScriptTester:
    """脚本测试器"""
    
    def __init__(self):
        self.browser = None
        self.context = None
        self.page = None
        self.test_results = []
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.playwright = await async_playwright().__aenter__()
        self.browser = await self.playwright.chromium.launch(
            headless=False,
            args=[
                '--disable-web-security',
                '--disable-blink-features=AutomationControlled',
                '--no-first-run'
            ]
        )
        self.context = await self.browser.new_context(
            viewport={"width": 1920, "height": 1080},
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        )
        self.page = await self.context.new_page()
        self.page.set_default_timeout(15000)  # 15秒超时
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.__aexit__(exc_type, exc_val, exc_tb)

    async def test_script_actions(self, actions_file: str) -> Dict:
        """测试脚本中的所有操作"""
        print(f"🧪 开始测试脚本: {actions_file}")
        
        # 加载操作配置
        with open(actions_file, 'r', encoding='utf-8') as f:
            actions = json.load(f)
        
        test_results = {
            'total_steps': len(actions),
            'passed_steps': 0,
            'failed_steps': 0,
            'fixed_steps': 0,
            'step_results': [],
            'success_rate': 0.0
        }
        
        # 逐步测试每个操作
        for i, action in enumerate(actions, 1):
            # 从narration中提取操作描述，如果太长则截取
            operation_desc = action.get('narration', 'N/A')
            if len(operation_desc) > 50:
                operation_desc = operation_desc[:47] + "..."
            print(f"🔍 测试步骤 {i}/{len(actions)}: {action.get('action', 'unknown')} - {operation_desc}")

            step_result = await self._test_single_step(action, i)
            test_results['step_results'].append(step_result)
            
            if step_result['status'] == 'passed':
                test_results['passed_steps'] += 1
            elif step_result['status'] == 'fixed':
                test_results['fixed_steps'] += 1
                test_results['passed_steps'] += 1
            else:
                test_results['failed_steps'] += 1
        
        # 计算成功率
        test_results['success_rate'] = (test_results['passed_steps'] / test_results['total_steps']) * 100
        
        print(f"📊 测试完成: {test_results['success_rate']:.1f}% 成功率")
        return test_results

    async def _test_single_step(self, action: Dict, step_num: int) -> Dict:
        """测试单个操作步骤"""
        step_result = {
            'step': step_num,
            'action': action.get('action'),
            'original_selector': action.get('selector'),
            'fixed_selector': None,
            'status': 'failed',
            'error': None,
            'suggestions': []
        }
        
        try:
            if action['action'] == 'goto':
                await self._test_goto(action, step_result)
            elif action['action'] == 'click':
                await self._test_click(action, step_result)
            elif action['action'] == 'click_skip_guide':
                await self._test_skip_guide(action, step_result)
            elif action['action'] == 'fill':
                await self._test_fill(action, step_result)
            elif action['action'] == 'wait':
                await self._test_wait(action, step_result)
            else:
                step_result['error'] = f"未知操作类型: {action['action']}"
                
        except Exception as e:
            step_result['error'] = str(e)
            
        return step_result

    async def _test_goto(self, action: Dict, result: Dict):
        """测试页面跳转"""
        try:
            url = action.get('value', 'http://xiaoyuanst.com')
            if not url.startswith('http'):
                url = 'http://' + url
                
            await self.page.goto(url, wait_until='networkidle', timeout=30000)
            result['status'] = 'passed'
            
        except Exception as e:
            result['error'] = str(e)
            result['suggestions'].append("检查网络连接和URL有效性")

    async def _test_click(self, action: Dict, result: Dict):
        """测试点击操作"""
        selector = action.get('selector', '')
        
        if not selector:
            result['error'] = "选择器为空"
            result['suggestions'].append("需要提供有效的选择器")
            return
            
        try:
            # 尝试原始选择器
            await self.page.wait_for_selector(selector, timeout=5000)
            await self.page.click(selector)
            result['status'] = 'passed'
            
        except Exception as e:
            # 尝试自动修复选择器
            fixed_selector = await self._auto_fix_selector(selector, 'click')
            if fixed_selector:
                try:
                    await self.page.click(fixed_selector)
                    result['status'] = 'fixed'
                    result['fixed_selector'] = fixed_selector
                    print(f"✅ 自动修复成功: {selector} -> {fixed_selector}")
                except:
                    result['error'] = f"原始选择器失败: {str(e)}, 修复后仍失败"
            else:
                result['error'] = str(e)
                result['suggestions'] = await self._get_click_suggestions(selector)

    async def _test_fill(self, action: Dict, result: Dict):
        """测试输入操作"""
        selector = action.get('selector', '')
        value = action.get('value', '')
        
        if not selector:
            result['error'] = "选择器为空"
            return
            
        try:
            await self.page.wait_for_selector(selector, timeout=5000)
            await self.page.fill(selector, value)
            result['status'] = 'passed'
            
        except Exception as e:
            # 尝试自动修复
            fixed_selector = await self._auto_fix_selector(selector, 'fill')
            if fixed_selector:
                try:
                    await self.page.fill(fixed_selector, value)
                    result['status'] = 'fixed'
                    result['fixed_selector'] = fixed_selector
                    print(f"✅ 自动修复成功: {selector} -> {fixed_selector}")
                except:
                    result['error'] = f"原始选择器失败: {str(e)}, 修复后仍失败"
            else:
                result['error'] = str(e)
                result['suggestions'] = await self._get_fill_suggestions(selector)

    async def _test_wait(self, action: Dict, result: Dict):
        """测试等待操作"""
        try:
            wait_time = int(action.get('value', 1000))
            await self.page.wait_for_timeout(wait_time)
            result['status'] = 'passed'
        except Exception as e:
            result['error'] = str(e)

    async def _test_skip_guide(self, action: Dict, result: Dict):
        """测试跳过引导操作"""
        try:
            # 使用智能引导检测和跳过
            from guide_detector import smart_skip_guides

            print("🎯 测试智能引导检测...")
            guide_result = await smart_skip_guides(self.page, max_attempts=2)

            if guide_result['final_state'] == 'ready':
                result['status'] = 'passed'
                result['fixed_selector'] = f"智能检测(处理了{guide_result['guides_skipped']}个引导)"
                print(f"✅ 智能引导检测成功: {guide_result}")
            elif guide_result['guides_found'] == 0:
                result['status'] = 'passed'
                result['fixed_selector'] = "无引导界面"
                print("✅ 未检测到引导界面，页面正常")
            else:
                result['status'] = 'passed'  # 即使部分失败也不算错误
                result['error'] = f"引导检测部分成功: 发现{guide_result['guides_found']}个，跳过{guide_result['guides_skipped']}个"
                print(f"⚠️ 引导检测部分成功: {guide_result}")

        except Exception as e:
            result['status'] = 'passed'  # 跳过引导操作失败不算错误
            result['error'] = f'智能引导检测异常: {str(e)}，但这是正常的'
            print(f"⚠️ 智能引导检测异常: {e}")

    async def _auto_fix_selector(self, original_selector: str, action_type: str) -> Optional[str]:
        """自动修复选择器"""
        if not original_selector:
            return None
            
        # 提取文本内容
        text_match = re.search(r'text=(.+)', original_selector)
        if not text_match:
            return None
            
        text_content = text_match.group(1)
        
        # 尝试多种选择器策略
        strategies = [
            f"text={text_content}",
            f"button:has-text('{text_content}')",
            f"a:has-text('{text_content}')",
            f"[title='{text_content}']",
            f"[alt='{text_content}']",
            f"[value='{text_content}']",
            f"*:has-text('{text_content}')",
        ]
        
        # 如果是输入框，添加输入框特定策略
        if action_type == 'fill':
            strategies.extend([
                f"input[placeholder*='{text_content}']",
                f"input[name*='{text_content.lower()}']",
                f"textarea[placeholder*='{text_content}']",
                "input[type='text']",
                "input[type='search']",
                "textarea"
            ])
        
        # 逐个测试策略
        for strategy in strategies:
            try:
                elements = await self.page.query_selector_all(strategy)
                if elements:
                    # 找到可见的元素
                    for element in elements:
                        if await element.is_visible():
                            return strategy
            except:
                continue
                
        return None

    async def _get_click_suggestions(self, selector: str) -> List[str]:
        """获取点击操作的修复建议"""
        suggestions = []
        
        try:
            # 检查页面上的可点击元素
            clickable_elements = await self.page.query_selector_all("button, a, [onclick], [role='button']")
            
            if clickable_elements:
                suggestions.append(f"页面上有 {len(clickable_elements)} 个可点击元素")
                
                # 获取前几个元素的文本
                for i, element in enumerate(clickable_elements[:5]):
                    try:
                        text = await element.inner_text()
                        if text.strip():
                            suggestions.append(f"可点击元素 {i+1}: '{text.strip()}'")
                    except:
                        pass
            else:
                suggestions.append("页面上没有找到可点击元素")
                
        except Exception as e:
            suggestions.append(f"无法分析页面元素: {str(e)}")
            
        return suggestions

    async def _get_fill_suggestions(self, selector: str) -> List[str]:
        """获取输入操作的修复建议"""
        suggestions = []
        
        try:
            # 检查页面上的输入元素
            input_elements = await self.page.query_selector_all("input, textarea, select")
            
            if input_elements:
                suggestions.append(f"页面上有 {len(input_elements)} 个输入元素")
                
                for i, element in enumerate(input_elements[:5]):
                    try:
                        placeholder = await element.get_attribute('placeholder')
                        name = await element.get_attribute('name')
                        element_type = await element.get_attribute('type')
                        
                        info = f"输入元素 {i+1}: type='{element_type}'"
                        if placeholder:
                            info += f", placeholder='{placeholder}'"
                        if name:
                            info += f", name='{name}'"
                            
                        suggestions.append(info)
                    except:
                        pass
            else:
                suggestions.append("页面上没有找到输入元素")
                
        except Exception as e:
            suggestions.append(f"无法分析页面元素: {str(e)}")
            
        return suggestions

    async def apply_fixes(self, actions_file: str, test_results: Dict) -> str:
        """应用修复到操作文件"""
        print("🔧 应用自动修复...")
        
        # 加载原始操作
        with open(actions_file, 'r', encoding='utf-8') as f:
            actions = json.load(f)
        
        # 应用修复
        fixed_count = 0
        for i, step_result in enumerate(test_results['step_results']):
            if step_result['status'] == 'fixed' and step_result['fixed_selector']:
                actions[i]['selector'] = step_result['fixed_selector']
                fixed_count += 1
        
        # 保存修复后的文件
        fixed_file = actions_file.replace('.json', '_fixed.json')
        with open(fixed_file, 'w', encoding='utf-8') as f:
            json.dump(actions, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 已修复 {fixed_count} 个步骤，保存到: {fixed_file}")
        return fixed_file

async def test_script_comprehensive(actions_file: str) -> Tuple[Dict, str]:
    """全面测试脚本并自动修复"""
    async with ScriptTester() as tester:
        # 执行测试
        test_results = await tester.test_script_actions(actions_file)
        
        # 应用修复
        fixed_file = await tester.apply_fixes(actions_file, test_results)
        
        return test_results, fixed_file

def main():
    """测试主函数"""
    import sys
    
    if len(sys.argv) != 2:
        print("用法: python script_tester.py <actions_file.json>")
        return
    
    actions_file = sys.argv[1]
    if not os.path.exists(actions_file):
        print(f"文件不存在: {actions_file}")
        return
    
    # 运行测试
    test_results, fixed_file = asyncio.run(test_script_comprehensive(actions_file))
    
    # 显示结果
    print("\n" + "="*60)
    print("📊 测试结果摘要")
    print("="*60)
    print(f"总步骤数: {test_results['total_steps']}")
    print(f"成功步骤: {test_results['passed_steps']}")
    print(f"修复步骤: {test_results['fixed_steps']}")
    print(f"失败步骤: {test_results['failed_steps']}")
    print(f"成功率: {test_results['success_rate']:.1f}%")
    print(f"修复后文件: {fixed_file}")

if __name__ == "__main__":
    main()
