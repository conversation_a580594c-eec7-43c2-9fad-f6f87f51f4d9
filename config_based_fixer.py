#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于配置文件的智能脚本修复器
读取项目配置文件，智能修复录制脚本
"""

import json
import asyncio
from typing import Dict, List, Optional
from playwright.async_api import async_playwright
from guide_detector import smart_skip_guides

class ConfigBasedFixer:
    """基于配置文件的智能修复器"""
    
    def __init__(self, config_file: str = "project_config.json"):
        self.config_file = config_file
        self.config = self._load_config()
        self.page = None
        self.browser = None
        self.context = None
        self.playwright = None
    
    def _load_config(self) -> Dict:
        """加载项目配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"✅ 成功加载配置文件: {self.config_file}")
            print(f"   - Blueprints: {len(config.get('blueprints', {}))}")
            print(f"   - Routes: {len(config.get('routes', {}))}")
            print(f"   - Templates: {len(config.get('templates', {}))}")
            return config
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            return {}
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(headless=False)
        self.context = await self.browser.new_context()
        self.page = await self.context.new_page()
        self.page.set_default_timeout(30000)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        try:
            if self.page and not self.page.is_closed():
                await self.page.close()
        except:
            pass
        
        try:
            if self.context:
                await self.context.close()
        except:
            pass
        
        try:
            if self.browser:
                await self.browser.close()
        except:
            pass
        
        try:
            if self.playwright:
                await self.playwright.stop()
        except:
            pass
    
    async def fix_script(self, actions_file: str) -> str:
        """修复脚本文件"""
        print(f"🎯 开始基于配置文件修复脚本: {actions_file}")
        
        # 加载原始脚本
        with open(actions_file, 'r', encoding='utf-8') as f:
            actions = json.load(f)
        
        # 初始化页面
        await self._initialize_page()
        
        # 修复每个操作
        fixed_actions = []
        fixed_count = 0
        
        for i, action in enumerate(actions, 1):
            print(f"🔍 修复步骤 {i}/{len(actions)}: {action.get('action')} - {action.get('narration', '')[:50]}...")
            
            fixed_action = await self._fix_single_action(action, i)
            
            # 检查是否有修复
            if fixed_action.get('selector') != action.get('selector'):
                fixed_count += 1
                print(f"✅ 步骤 {i} 修复成功")
            
            fixed_actions.append(fixed_action)
        
        # 保存修复后的脚本
        fixed_file = actions_file.replace('.json', '_config_fixed.json')
        with open(fixed_file, 'w', encoding='utf-8') as f:
            json.dump(fixed_actions, f, ensure_ascii=False, indent=2)
        
        print(f"📊 配置文件修复完成: 修复了 {fixed_count}/{len(actions)} 个步骤")
        print(f"✅ 修复后文件保存到: {fixed_file}")
        return fixed_file
    
    async def _initialize_page(self):
        """初始化页面"""
        print("🌐 初始化页面...")
        
        # 访问网站
        await self.page.goto("http://xiaoyuanst.com", wait_until='networkidle')
        
        # 点击体验系统
        await self.page.click("text=体验系统")
        await self.page.wait_for_timeout(3000)
        
        # 智能跳过引导
        guide_result = await smart_skip_guides(self.page)
        print(f"✅ 引导跳过完成: {guide_result['final_state']}")
        
        # 等待页面稳定
        await self.page.wait_for_timeout(2000)
    
    async def _fix_single_action(self, action: Dict, step_num: int) -> Dict:
        """修复单个操作"""
        fixed_action = action.copy()
        
        action_type = action.get('action')
        
        if action_type == 'click':
            fixed_selector = await self._fix_click_selector(action)
            if fixed_selector and fixed_selector != action.get('selector'):
                fixed_action['selector'] = fixed_selector
                fixed_action['original_selector'] = action.get('selector')
                fixed_action['fix_method'] = 'config_based'
        
        elif action_type == 'fill':
            fixed_selector = await self._fix_fill_selector(action)
            if fixed_selector and fixed_selector != action.get('selector'):
                fixed_action['selector'] = fixed_selector
                fixed_action['original_selector'] = action.get('selector')
                fixed_action['fix_method'] = 'config_based'
        
        return fixed_action
    
    async def _fix_click_selector(self, action: Dict) -> Optional[str]:
        """修复点击选择器"""
        original_selector = action.get('selector', '')
        if not original_selector:
            return original_selector
        
        # 提取文本内容
        text_content = self._extract_text_from_selector(original_selector)
        if not text_content:
            return original_selector
        
        print(f"🎯 基于配置修复点击选择器: {text_content}")
        
        # 1. 从配置文件的导航映射中查找
        nav_selector = self._get_navigation_selector(text_content)
        if nav_selector and await self._test_selector(nav_selector):
            print(f"✅ 导航映射修复成功: {nav_selector}")
            return nav_selector
        
        # 2. 从配置文件的选择器映射中查找
        config_selector = self._get_config_selector(text_content, 'click')
        if config_selector and await self._test_selector(config_selector):
            print(f"✅ 配置选择器修复成功: {config_selector}")
            return config_selector
        
        # 3. 基于路由信息生成选择器
        route_selector = self._get_route_based_selector(text_content)
        if route_selector and await self._test_selector(route_selector):
            print(f"✅ 路由选择器修复成功: {route_selector}")
            return route_selector
        
        # 4. 基于模板信息生成选择器
        template_selector = await self._get_template_based_selector(text_content)
        if template_selector and await self._test_selector(template_selector):
            print(f"✅ 模板选择器修复成功: {template_selector}")
            return template_selector
        
        return original_selector
    
    async def _fix_fill_selector(self, action: Dict) -> Optional[str]:
        """修复填写选择器"""
        # 填写操作的修复逻辑
        return action.get('selector', '')
    
    def _extract_text_from_selector(self, selector: str) -> str:
        """从选择器中提取文本"""
        import re
        
        # text= 格式
        text_match = re.search(r'text=(.+)', selector)
        if text_match:
            return text_match.group(1).strip('"\'')
        
        # :has-text() 格式
        has_text_match = re.search(r':has-text\(["\']([^"\']+)["\']', selector)
        if has_text_match:
            return has_text_match.group(1)
        
        return ''
    
    def _get_navigation_selector(self, text: str) -> Optional[str]:
        """从导航映射中获取选择器"""
        navigation = self.config.get('navigation', {})
        
        # 查找供应商模块
        supplier_nav = navigation.get('supplier', {})
        children = supplier_nav.get('children', {})
        
        for nav_name, nav_info in children.items():
            if text in nav_name or nav_name in text:
                selectors = nav_info.get('selectors', {})
                # 返回菜单链接选择器
                return selectors.get('menu_link')
            
            # 检查操作按钮
            actions = nav_info.get('actions', [])
            if text in actions:
                selectors = nav_info.get('selectors', {})
                # 根据操作类型返回对应选择器
                if '添加' in text:
                    return selectors.get('add_button')
                elif '编辑' in text:
                    return selectors.get('edit_button')
                elif '删除' in text:
                    return selectors.get('delete_button')
                elif '查看' in text:
                    return selectors.get('view_button')
        
        return None
    
    def _get_config_selector(self, text: str, action_type: str) -> Optional[str]:
        """从选择器配置中获取选择器"""
        selectors = self.config.get('selectors', {})
        
        # 检查操作类型选择器
        actions = selectors.get('actions', {})
        
        if '添加' in text:
            candidates = actions.get('add', [])
        elif '编辑' in text:
            candidates = actions.get('edit', [])
        elif '删除' in text:
            candidates = actions.get('delete', [])
        elif '查看' in text:
            candidates = actions.get('view', [])
        elif '保存' in text or '提交' in text:
            candidates = actions.get('save', [])
        elif '搜索' in text:
            candidates = actions.get('search', [])
        else:
            # 检查通用选择器
            common = selectors.get('common', {})
            if '提交' in text:
                candidates = common.get('submit_button', [])
            elif '取消' in text:
                candidates = common.get('cancel_button', [])
            elif '确认' in text:
                candidates = common.get('confirm_button', [])
            elif '返回' in text:
                candidates = common.get('back_button', [])
            else:
                candidates = []
        
        # 返回第一个候选选择器
        return candidates[0] if candidates else None
    
    def _get_route_based_selector(self, text: str) -> Optional[str]:
        """基于路由信息生成选择器"""
        routes = self.config.get('routes', {})
        
        # 查找相关路由
        for route_key, route_info in routes.items():
            if 'supplier' in route_key.lower():
                path = route_info.get('path', '')
                
                # 根据路由路径生成选择器
                if 'category' in path and '分类' in text:
                    return f'a[href*="/supplier_category/"]:has-text("{text}")'
                elif 'product' in path and '产品' in text:
                    return f'a[href*="/supplier_product/"]:has-text("{text}")'
                elif '/supplier/' in path and '供应商' in text:
                    return f'a[href*="/supplier/"]:has-text("{text}")'
        
        return None
    
    async def _get_template_based_selector(self, text: str) -> Optional[str]:
        """基于模板信息生成选择器"""
        templates = self.config.get('templates', {})
        
        # 查找供应商相关模板
        for template_path, template_info in templates.items():
            if 'supplier' in template_path:
                buttons = template_info.get('buttons', [])
                links = template_info.get('links', [])
                
                # 检查按钮
                for button in buttons:
                    if text in button.get('text', ''):
                        css_class = button.get('css_class', '')
                        if css_class:
                            return f'.{css_class.split()[0]}:has-text("{text}")'
                
                # 检查链接
                for link in links:
                    if text in link.get('text', ''):
                        href = link.get('href', '')
                        if href and not href.startswith('#'):
                            return f'a[href*="{href}"]:has-text("{text}")'
        
        return None
    
    async def _test_selector(self, selector: str) -> bool:
        """测试选择器是否有效"""
        try:
            elements = await self.page.query_selector_all(selector)
            if not elements:
                return False
            
            # 检查是否有可见元素
            for element in elements:
                if await element.is_visible():
                    return True
            
            return False
        except Exception:
            return False

async def config_based_fix_script(actions_file: str, config_file: str = "project_config.json") -> str:
    """基于配置文件修复脚本的便捷函数"""
    async with ConfigBasedFixer(config_file) as fixer:
        return await fixer.fix_script(actions_file)

# 测试函数
async def test_config_based_fixer():
    """测试基于配置文件的修复器"""
    actions_file = 'output/供应商模块视频录制脚本_带时间戳_actions.json'
    fixed_file = await config_based_fix_script(actions_file)
    print(f"🎉 测试完成，修复文件: {fixed_file}")

if __name__ == "__main__":
    asyncio.run(test_config_based_fixer())
